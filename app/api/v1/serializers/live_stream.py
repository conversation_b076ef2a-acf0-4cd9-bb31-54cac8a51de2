from datetime import timedelta

from django.conf import settings
from django.utils import timezone
from django.utils.timezone import now
from rest_framework import serializers

from app.api.fields import <PERSON>Field
from app.models import LiveStream, LiveStreamEvent
from app.utils.live_stream_time_converter import (
    convert_timestamps_if_needed,
    detect_timestamp_types,
    LiveStreamTimeConversionError,
)


class ResolutionsLabelField(serializers.Field):
    def to_representation(self, resolutions):
        return [LiveStream.Resolutions(resolution).label for resolution in resolutions]


class LiveStreamMessages:
    ENDED_WITH_RECORDING = (
        "The live stream has come to an end. Stay tuned, "
        "we'll have the recording ready for you shortly."
    )
    ENDED_WITHOUT_RECORDING = "The live stream has ended. See you at the next one!"
    IMMINENT_START = "Hang tight! The live stream will kick off in a moment."
    FUTURE_START = "Hang tight! The live stream will kick off at {}."


class LiveStreamSerializer(serializers.ModelSerializer):
    rtmp_url = serializers.CharField(read_only=True)
    stream_key = serializers.CharField(read_only=True)
    title = serializers.CharField(write_only=True)
    status = serializers.ReadOnlyField(source="get_status_display")
    hls_url = serializers.ReadOnlyField(source="get_hls_url")
    dash_url = serializers.ReadOnlyField(source="get_dash_url")
    start = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False)
    chat_embed_url = serializers.SerializerMethodField(read_only=True)
    chat_transcript_url = serializers.SerializerMethodField(read_only=True)
    resolutions = ResolutionsLabelField(read_only=True)
    transcode_recorded_video = serializers.BooleanField(initial=True, required=False)
    store_recorded_video = serializers.BooleanField(initial=True, required=False)
    latency = ChoiceField(choices=LiveStream.Latency.choices, required=False)
    notice_message = serializers.SerializerMethodField()

    def get_chat_embed_url(self, obj):
        if obj.chat_room_id:
            return f"{settings.SITE_URL}/live-chat/{obj.asset.organization.uuid}/{obj.asset.uuid}/"
        return None

    def get_chat_transcript_url(self, obj):
        if obj.chat_transcript_url:
            return f"{obj.organization.cdn_url}{obj.chat_transcript_url}"
        return None

    def validate_start(self, value):
        if value and value < (now() - timedelta(minutes=1)):
            raise serializers.ValidationError(
                "Start time must be greater than current time"
            )
        return value

    def get_notice_message(self, obj):
        current_time = now()

        if obj.status == LiveStream.Status.COMPLETED:
            if obj.transcode_recorded_video and not obj.is_recording_transcoded:
                return LiveStreamMessages.ENDED_WITH_RECORDING
            if not obj.transcode_recorded_video:
                return LiveStreamMessages.ENDED_WITHOUT_RECORDING

        if obj.status == LiveStream.Status.NOT_STARTED:
            if obj.start and current_time < obj.start:
                start_time_formatted = timezone.localtime(obj.start).strftime(
                    "%B %d, %Y, %I:%M %p"
                )
                return LiveStreamMessages.FUTURE_START.format(start_time_formatted)
            return LiveStreamMessages.IMMINENT_START

        return None

    class Meta:
        model = LiveStream
        fields = [
            "rtmp_url",
            "stream_key",
            "title",
            "status",
            "hls_url",
            "dash_url",
            "start",
            "transcode_recorded_video",
            "store_recorded_video",
            "enable_drm_for_recording",
            "chat_embed_url",
            "chat_transcript_url",
            "chat_room_id",
            "resolutions",
            "enable_drm",
            "enable_llhls",
            "latency",
            "notice_message",
        ]


class ActivitySerializer(serializers.ModelSerializer):
    status = serializers.CharField(source="get_type_display")
    timestamp = serializers.SerializerMethodField()

    def get_timestamp(self, obj):
        return timezone.localtime(obj.created).strftime("%B %d, %Y, %I:%M %p")

    class Meta:
        model = LiveStreamEvent
        fields = ["status", "timestamp"]


class LiveStreamAdminSerializer(LiveStreamSerializer):
    activities = serializers.SerializerMethodField(read_only=True)

    def get_activities(self, obj):
        return ActivitySerializer(
            obj.events.filter(
                type__in=[
                    LiveStreamEvent.Type.ON_PUBISH,
                    LiveStreamEvent.Type.ON_PUBISH_DONE,
                    LiveStreamEvent.Type.STOPPED,
                ]
            ),
            many=True,
        ).data

    class Meta(LiveStreamSerializer.Meta):
        fields = LiveStreamSerializer.Meta.fields + ["activities"]


class LiveStreamTrimRequestSerializer(serializers.Serializer):
    start_time = serializers.IntegerField(min_value=0, required=False)
    end_time = serializers.IntegerField(min_value=0, required=False)

    def __init__(self, *args, **kwargs):
        self.duration = kwargs.pop("duration", None)
        self.live_stream = kwargs.pop("live_stream", None)
        super().__init__(*args, **kwargs)

    def validate(self, data):
        if self.duration is None:
            raise serializers.ValidationError("Duration is required for validation.")

        original_start_time = data.get("start_time")
        original_end_time = data.get("end_time")

        # Detect if timestamps are epoch or relative seconds
        start_is_epoch, end_is_epoch = detect_timestamp_types(
            original_start_time, original_end_time
        )

        # Validate that timestamps are consistent (all epoch or all relative)
        if start_is_epoch != end_is_epoch and original_start_time is not None and original_end_time is not None:
            raise serializers.ValidationError(
                "Cannot mix epoch timestamps and relative seconds in the same request. "
                "Use either all epoch timestamps or all relative seconds."
            )

        # Determine if we're dealing with epoch timestamps
        using_epoch = start_is_epoch or end_is_epoch

        # If using epoch timestamps, we need the live stream to get HLS manifest URL
        if using_epoch and self.live_stream is None:
            raise serializers.ValidationError(
                "Live stream instance is required for epoch timestamp conversion."
            )

        # Convert epoch timestamps to relative seconds if needed
        if using_epoch:
            try:
                hls_manifest_url = self.live_stream.get_hls_url
                start_time, end_time = convert_timestamps_if_needed(
                    original_start_time, original_end_time, hls_manifest_url
                )
            except LiveStreamTimeConversionError as e:
                raise serializers.ValidationError(f"Time conversion failed: {str(e)}")
        else:
            start_time = original_start_time
            end_time = original_end_time

        # Apply defaults for None values
        start_time = start_time if start_time is not None else 0
        end_time = end_time if end_time is not None else self.duration

        # Validate converted relative seconds
        if start_time < 0 or start_time > self.duration:
            if using_epoch:
                raise serializers.ValidationError(
                    f"Converted start_time ({start_time}s) is out of valid range (0-{self.duration}s). "
                    f"Original epoch timestamp: {original_start_time}"
                )
            else:
                raise serializers.ValidationError("start_time is out of valid range.")

        if end_time < 0 or end_time > self.duration:
            if using_epoch:
                raise serializers.ValidationError(
                    f"Converted end_time ({end_time}s) is out of valid range (0-{self.duration}s). "
                    f"Original epoch timestamp: {original_end_time}"
                )
            else:
                raise serializers.ValidationError("end_time is out of valid range.")

        if start_time >= end_time:
            raise serializers.ValidationError("start_time must be less than end_time.")

        data["start_time"] = start_time
        data["end_time"] = end_time

        # Store original values for debugging/logging purposes
        data["_original_start_time"] = original_start_time
        data["_original_end_time"] = original_end_time
        data["_using_epoch"] = using_epoch

        return data
