import boto3
import sentry_sdk
from django.conf import settings
from django.urls import reverse

from app.utils.base64 import url_safe_base64_encode
from app.utils.live_stream import LiveStreamServer

THIRTY_MINUTES = 30 * 60


def create_aws_live_stream_server(name, asset):
    from app.domain.live_stream import is_transcoding_needed

    organization = asset.organization
    ec2 = boto3.client(
        "ec2",
        aws_access_key_id=settings.TPSTREAMS_AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY,
        region_name="ap-south-1",
    )

    SNAPSHOT_ID = settings.LIVE_STREAM_AWS_SNAPSHOT
    VOLUME_SNAPSHOT_ID = settings.EC2_LIVESTREAM_VOLUME_ID

    if asset.live_stream.enable_llhls:
        SNAPSHOT_ID = "ami-0ab97c3db751a1d00"
        VOLUME_SNAPSHOT_ID = "snap-0302bc07a57473ce0"

    output_path = get_output_path(asset)

    server_type = "c6g.xlarge" if organization.enable_hd_live_streaming else "c6g.large"
    volume_size = 60 if organization.enable_hd_live_streaming else 40
    if asset.live_stream.enable_drm:
        server_type = "c6g.xlarge"

    if organization.uuid in [
        "bn5yhg",
        "9mpasc",
        "n2nbhp",
        "bg5yak",
        "f87hk3",
        "h8g4g4",
        "mct2nn",
    ]:  # Insights
        server_type = "c6g.xlarge"

    if asset.live_stream.enable_drm and organization.uuid in [
        "f87hk3",
        "h8g4g4",
        "mct2nn",
    ]:
        server_type = "c6g.2xlarge"
        volume_size = 60

    if asset.live_stream.enable_drm and organization.enable_hd_live_streaming:
        server_type = "c6g.2xlarge"
    store_backup_in_aws = is_transcoding_needed(asset.live_stream)
    shutdown_behavior = "stop" if not settings.DEBUG else "terminate"
    termination_protection = not settings.DEBUG
    instances = ec2.run_instances(
        ImageId=SNAPSHOT_ID,
        MinCount=1,
        MaxCount=1,
        InstanceType=server_type,
        InstanceInitiatedShutdownBehavior=shutdown_behavior,
        DisableApiTermination=termination_protection,
        SecurityGroupIds=[
            "sg-03e5b328925995fd8",
        ],
        TagSpecifications=[
            {
                "ResourceType": "instance",
                "Tags": [
                    {"Key": "Name", "Value": name},
                ],
            },
        ],
        UserData=get_user_data(organization, output_path, store_backup_in_aws, asset),
        Placement={"AvailabilityZone": "ap-south-1b"},
        SubnetId="subnet-044bdb8a99f2487df",
        BlockDeviceMappings=[
            {
                "DeviceName": "/dev/sda1",
                "Ebs": {
                    "SnapshotId": VOLUME_SNAPSHOT_ID,
                    "VolumeSize": volume_size,
                    "DeleteOnTermination": True,
                    "VolumeType": "gp3",
                },
            },
        ],
    )
    return _parse_live_stream_server_response(instances["Instances"][0])


def get_output_path(asset):
    from app.domain.live_stream import is_transcoding_needed

    organization = asset.organization
    output_path = f"s3://{asset.organization.bucket_name}/transcoded/{asset.uuid}/"

    requires_transcoding = is_transcoding_needed(asset.live_stream)
    if requires_transcoding:
        output_path = f"s3://{settings.LIVE_STREAM_S3_BUCKET}/live_streams/{organization.uuid}/{asset.uuid}/"

    return output_path


def get_user_data(organization, output_path, store_backup_in_aws, asset):
    org_uuid = organization.uuid
    callback_url = settings.SITE_URL + reverse(
        "api:live-stream-server-callback",
        kwargs={
            "organization_id": org_uuid,
        },
    )

    upload_to_S3_script = f"""
      - path: /home/<USER>/rclone.sh
        permissions: '0755'
        owner: root
        content: |
          #!/bin/bash

          rclone copy /home/<USER>/livestream/ {output_path} --config=/home/<USER>/.s3_config --exclude "/video.mp4" --exclude "*.txt"
    """
    git_commands = """
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow pull origin main
    """

    if org_uuid == "f87hk3" or org_uuid == "h8g4g4" or org_uuid == "mct2nn":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout vbr
        """

    if org_uuid == "edee9b":
        git_commands = """
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout vbr_testing
        """

    if org_uuid == "jh5tdb":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout vidyakul_low_bandwidth
        """

    if org_uuid == "hd66qr":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout high_bitrate
        """

    if org_uuid == "n2nbhp":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout variable_bitrate
        """

    if org_uuid == "bg5yak":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout Utkarsh-Classes
        """

    if org_uuid == "dcek2m":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout debug-rclone
        """

    if org_uuid == "9jfu42":
        git_commands = """
      - mkdir /home/<USER>/livestream/nonfrag
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow fetch
      - sudo -u ubuntu git -C /home/<USER>/workspace/liveflow/live-flow checkout add_program_date_time
        """

    schedule_termination_command = ""
    if settings.DEBUG:
        schedule_termination_command = (
            f"sleep {settings.DURATION_TO_AUTO_TERMINATE} && sudo shutdown -h now"
        )
    aws_access_key = settings.TPSTREAMS_AWS_ACCESS_KEY_ID
    aws_secret_key = settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY
    access_key = organization.storage_access_key_id
    secret_key = organization.storage_secret_access_key
    region = organization.storage_region
    endpoint = (
        f"https://s3.{region}.amazonaws.com"
        if organization.get_storage_vendor_display() == "Aws"
        else f"https://s3.{region}.wasabisys.com"
    )
    provider = "AWS" if organization.get_storage_vendor_display() == "Aws" else "Wasabi"
    return f"""
    #cloud-config
    write_files:
      - path: /tmp/post_data.sh
        permissions: '0755'
        owner: root
        content: |
          #!/bin/bash
          SERVER_ID=$(curl -s http://***************/latest/meta-data/instance-id)
          IP_ADDRESS=$(curl -s http://***************/latest/meta-data/public-ipv4)
          URL={callback_url}

          JSON_DATA=$(cat <<EOF
          {{
            "server_id": "$SERVER_ID",
            "ip_address": "$IP_ADDRESS",
            "status": "created"
          }}
          EOF
          )

          curl -X POST -H "Content-Type: application/json" -d "$JSON_DATA" "$URL"
      - path: /home/<USER>/.s3_config
        permissions: '0755'
        owner: root
        content: |
          [s3]
          type = s3
          provider = {'AWS' if store_backup_in_aws else provider}
          access_key_id = {aws_access_key if store_backup_in_aws else access_key}
          secret_access_key = {aws_secret_key if store_backup_in_aws else secret_key}
          storage_class = STANDARD
          acl = public-read
          {'region = '+ region if not store_backup_in_aws else ''}
          {'endpoint = '+ endpoint if not store_backup_in_aws else ""}
      {upload_to_S3_script}

    runcmd:
      - SERVER_ID=$(curl -s http://***************/latest/meta-data/instance-id)
      - echo "SERVER_ID=${{SERVER_ID}}" >> /etc/environment
      - echo 'ORG_CODE="{org_uuid}"' >> /etc/environment
      - echo 'WASABI_ACCESS_KEY_ID="{organization.storage_access_key_id}"' >> /etc/environment
      - echo 'WASABI_BUCKET_REGION="{organization.storage_region}"' >> /etc/environment
      - echo 'WASABI_SECRET_ACCESS_KEY="{organization.storage_secret_access_key}"' >> /etc/environment
      - echo 'TPSTREAMS_URL="{settings.SITE_URL}"' >> /etc/environment
      - SERVER_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
      - echo "SERVER_IP=$SERVER_IP" >> /etc/environment
      - source /etc/environment
      - apt-get install -y curl
      - /tmp/post_data.sh
      - cd /home/<USER>/workspace/liveflow/live-flow/
      {git_commands}
      - /home/<USER>/workspace/liveflow/bin/pip install django-model-utils==4.3.1 django-cors-headers==4.0.0
      - /home/<USER>/workspace/liveflow/bin/python /home/<USER>/workspace/liveflow/live-flow/manage.py migrate --noinput
      - sudo chown ubuntu:ubuntu /home/<USER>/workspace/liveflow/live-flow/db.sqlite3
      - sudo chmod 664 /home/<USER>/workspace/liveflow/live-flow/db.sqlite3
      - /home/<USER>/workspace/liveflow/bin/python /home/<USER>/workspace/liveflow/live-flow/manage.py collectstatic --noinput
      - /home/<USER>/workspace/liveflow/bin/python /home/<USER>/workspace/liveflow/live-flow/manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username='admin').exists() or User.objects.create_superuser('admin', '<EMAIL>', '{asset.uuid}{organization.uuid}')"
      - wget https://github.com/shaka-project/shaka-packager/releases/download/v2.6.1/packager-linux-arm64
      - sudo mv packager-linux-arm64 /usr/local/bin/packager
      - sudo chmod +rx /usr/local/bin/packager
      - wget https://github.com/Karthik-0/gohlslib/releases/download/v31/muxer_linux_arm64
      - sudo mv muxer_linux_arm64 /usr/local/bin/muxer
      - sudo chmod +rx /usr/local/bin/muxer
      - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", SERVER_IP="'"${{SERVER_IP}}"'", ORG_CODE="{org_uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{organization.storage_access_key_id}", WASABI_BUCKET_REGION="{organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/celeryd.conf
      - sed -i 's|^environment=.*|&SERVER_ID="'"${{SERVER_ID}}"'", SERVER_IP="'"${{SERVER_IP}}"'", ORG_CODE="{org_uuid}", TPSTREAMS_URL="{settings.SITE_URL}", WASABI_ACCESS_KEY_ID="{organization.storage_access_key_id}", WASABI_BUCKET_REGION="{organization.storage_region}", WASABI_SECRET_ACCESS_KEY="{organization.storage_secret_access_key}"|' /etc/supervisor/conf.d/gunicorn.conf
      - systemctl enable supervisor
      - systemctl start supervisor
      - supervisorctl reread
      - supervisorctl update
      - supervisorctl start all
      - cd /home/<USER>
      - ./rclone.sh
      - {schedule_termination_command}
    """


def _parse_live_stream_server_response(data):
    from app.models import LiveStream, LiveStreamUsage

    server_id = data["InstanceId"]
    status = LiveStream.ServerStatus.CREATING
    if data["State"]["Name"] == "running":
        status = LiveStream.ServerStatus.CREATED
    elif data.get("status") == "pending":
        status = LiveStream.ServerStatus.CREATING

    private_ip = data.get("NetworkInterfaces", [])[0].get("PrivateIpAddress")

    try:
        public_ip = (
            data.get("NetworkInterfaces", [])[0].get("Association", {}).get("PublicIp")
        )
    except (AttributeError, IndexError):
        public_ip = None

    return LiveStreamServer(
        ip_address=public_ip,
        id=server_id,
        cost_per_hour=None,
        status=status,
        provider=LiveStreamUsage.ServerProvider.AWS,
        private_ip_address=private_ip,
    )


def delete_aws_server(server_id, region_name="ap-south-1"):
    ec2 = boto3.client(
        "ec2",
        aws_access_key_id=settings.TPSTREAMS_AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY,
        region_name=region_name,
    )
    try:
        add_name_to_ebs_volume(server_id, region_name)
        response = ec2.modify_instance_attribute(
            InstanceId=server_id, DryRun=False, DisableApiTermination={"Value": False}
        )
        if "Error" in response:
            sentry_sdk.capture_message(
                f"Error modifying instance attributes for server {server_id}: {response['Error']}",
                level="error",
            )
        termination_response = ec2.terminate_instances(
            InstanceIds=[server_id], DryRun=False
        )
        if "Error" in termination_response:
            sentry_sdk.capture_message(
                f"Error terminating instance {server_id}: {termination_response['Error']}",
                level="error",
            )
    except Exception as e:
        sentry_sdk.capture_message(
            f"Exception occurred while deleting server {server_id}: {str(e)}",
            level="error",
        )


def add_name_to_ebs_volume(server_id, region_name="ap-south-1"):
    ec2 = boto3.client(
        "ec2",
        aws_access_key_id=settings.TPSTREAMS_AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY,
        region_name=region_name,
    )
    instance_details = ec2.describe_instances(InstanceIds=[server_id])["Reservations"][
        0
    ]["Instances"][0]

    instance_name = None
    for tag in instance_details.get("Tags", []):
        if tag["Key"] == "Name":
            instance_name = tag["Value"]
            break

    root_volume_id = None
    for volume in instance_details.get("BlockDeviceMappings", []):
        if volume["DeviceName"] == "/dev/sda1":
            root_volume_id = volume["Ebs"]["VolumeId"]
            break

    if root_volume_id and instance_name:
        ec2.create_tags(
            Resources=[root_volume_id], Tags=[{"Key": "Name", "Value": instance_name}]
        )


class AWSSubtitleServer:
    SERVER_TYPE = "g5.xlarge"
    IMAGE_ID = "ami-001feddb0b1caf76d"
    REGION_NAME = "us-east-1"
    SECURITY_GROUP_ID = "sg-055ea6ef1874cf12a"
    VOLUME_SIZE = 64
    VOLUME_TYPE = "gp3"
    SERVER_TYPE_COST_PER_HOUR_DOLLAR = (
        1.006  # calculate & update based on aws pricing docs
    )
    VOLUME_COST_PER_GB_PER_HOUR_DOLLAR = (
        0.000138  # calculate & update based on aws pricing docs
    )

    def __init__(self, track):
        self.track = track
        self.asset = track.video.asset
        self.organization = track.organization
        self.audio_duration = track.subtitle_data.get("audio_duration", None)
        self.audio_input_url = track.subtitle_data.get("audio_input_url", None)

    @classmethod
    def get_server_details(cls):
        return {
            "volume_size": cls.VOLUME_SIZE,
            "server_type_cost_per_hour_dollar": cls.SERVER_TYPE_COST_PER_HOUR_DOLLAR,
            "volume_cost_per_gb_per_hour_dollar": cls.VOLUME_COST_PER_GB_PER_HOUR_DOLLAR,
        }

    @classmethod
    def get_server_cost_estimate_per_minute_in_dollar(
        cls, serever_type_cost, volume_cost, volume_size
    ):
        server_type_cost_per_minute = serever_type_cost / 60
        volume_cost_per_minute = (volume_cost / 60) * volume_size
        server_cost_per_minute = server_type_cost_per_minute + volume_cost_per_minute
        return server_cost_per_minute

    def create_server(self):
        user_data_script = self.get_user_data_script()
        if user_data_script:
            return self.spawn_subtitle_server(user_data_script)

    def get_user_data_script(self):
        if not (self.audio_duration and self.audio_input_url):
            return
        self.callback_url = self.get_callback_url()
        auto_terminate_duration = self.get_duration_to_auto_terminate(
            self.audio_duration
        )
        user_data_script = f"""#!/bin/bash
        {self.trigger_auto_termination(auto_terminate_duration)}
        {self.download_audio(self.audio_input_url)}
        {self.generate_subtitles()}
        {self.upload_subtitles_to_storage()}
        """
        return (
            user_data_script if (self.audio_duration and self.audio_input_url) else None
        )

    def get_callback_url(self):
        url = settings.SITE_URL + reverse(
            "api:subtitle-server-callback",
            kwargs={
                "organization_id": self.organization.uuid,
                "subtitle_id": self.track.id,
            },
        )
        server_token = self.get_server_token()
        return f"{url}?server_token={server_token}"

    def get_server_token(self):
        return url_safe_base64_encode(
            f"{self.organization.uuid}|{self.asset.uuid}|{self.track.id}"
        ).decode("utf-8")

    def get_duration_to_auto_terminate(self, audio_duration):
        return audio_duration + THIRTY_MINUTES

    def trigger_auto_termination(self, duration):
        return f"""
        sleep {duration} && curl -X POST -H "Content-Type: application/json" -d '{{"status":"AUTO_TERMINATED"}}' {self.callback_url} && sudo shutdown -h now &
        """

    def download_audio(self, audio_input_url):
        return f"""
        wget "{audio_input_url}" -O /home/<USER>/output_audio.mp3 && \
        curl -X POST -H "Content-Type: application/json" -d '{{"status":"AUDIO_DOWNLOADED"}}' {self.callback_url} || \
        (curl -X POST -H "Content-Type: application/json" -d '{{"status":"AUDIO_DOWNLOAD_FAILED"}}' {self.callback_url} && exit 1)
        """

    def generate_subtitles(self):
        return f"""
        if [ -f /home/<USER>/output_audio.mp3 ]; then
            whisper /home/<USER>/output_audio.mp3 --model large --task translate --output_format vtt > /home/<USER>/output.vtt && \
            curl -X POST -H "Content-Type: application/json" -d '{{"status":"SUBTITLE_GENERATED"}}' {self.callback_url}
        else
            curl -X POST -H "Content-Type: application/json" -d '{{"status":"SUBTITLE_GENERATION_FAILED"}}' {self.callback_url}
            exit 1
        fi
        """

    def upload_subtitles_to_storage(self):
        endpoint_url, bucket_name, path = self.get_storage_details()
        return f"""
        export AWS_ACCESS_KEY_ID={self.organization.storage_access_key_id}
        export AWS_SECRET_ACCESS_KEY={self.organization.storage_secret_access_key}
        export AWS_DEFAULT_REGION={self.organization.storage_region}
        aws s3 cp /output_audio.vtt s3://{bucket_name}/{path} --acl public-read --endpoint-url {endpoint_url} && \
        curl -X POST -H "Content-Type: application/json" -d '{{"status":"UPLOADED"}}' {self.callback_url} || \
        (curl -X POST -H "Content-Type: application/json" -d '{{"status":"UPLOAD_FAILED"}}' {self.callback_url} && exit 1)
        """

    def get_storage_details(self):
        endpoint_url = f"https://{self.organization.storage_domain}"
        bucket_name = self.organization.bucket_name
        path = f"transcoded/{self.asset.uuid}/subtitles/en-autogenerated.vtt"
        return endpoint_url, bucket_name, path

    def spawn_subtitle_server(self, user_data_script):
        ec2 = boto3.client(
            "ec2",
            aws_access_key_id=settings.TPSTREAMS_AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY,
            region_name=self.REGION_NAME,
        )
        instances = ec2.run_instances(
            ImageId=self.IMAGE_ID,
            MinCount=1,
            MaxCount=1,
            InstanceType=self.SERVER_TYPE,
            TagSpecifications=[
                {
                    "ResourceType": "instance",
                    "Tags": [
                        {
                            "Key": "Name",
                            "Value": f"Sub-{self.organization.uuid}-{self.asset.uuid}-{self.track.id}",
                        },
                    ],
                },
            ],
            SecurityGroupIds=[
                self.SECURITY_GROUP_ID,
            ],
            UserData=user_data_script,
            BlockDeviceMappings=[
                {
                    "DeviceName": "/dev/sda1",
                    "Ebs": {
                        "VolumeSize": self.VOLUME_SIZE,
                        "DeleteOnTermination": True,
                        "VolumeType": self.VOLUME_TYPE,
                    },
                },
            ],
            KeyName=settings.SUBTITLE_SERVER_KEY_PAIR_NAME,
        )
        return instances["Instances"][0]["InstanceId"]


class AWSOpenRestyServer:
    def __init__(self, **kwargs):
        self.server_type = kwargs.get("server_type")
        self.image_id = kwargs.get("image_id")
        self.region_name = kwargs.get("region_name")
        self.security_group_id = kwargs.get("security_group_id")
        self.volume_size = kwargs.get("volume_size")
        self.volume_type = kwargs.get("volume_type")

    def create_server(self, name):
        user_data_script = self.get_user_data_script_for_openresty()
        if user_data_script:
            return self.spawn_proxy_server(name, user_data_script)

    def get_user_data_script_for_openresty(self):
        user_data_script = f"""#!/bin/bash
        # Clone the openresty-docker repository
        sudo -u ubuntu <NAME_EMAIL>:testpress/openresty-docker.git
        cd ~/openresty-docker
        sudo bash start.sh
        """
        return user_data_script

    def spawn_proxy_server(self, name, user_data_script):
        ec2 = boto3.client(
            "ec2",
            aws_access_key_id=settings.TPSTREAMS_AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.TPSTREAMS_AWS_SECRET_ACCESS_KEY,
            region_name=self.region_name,
        )
        instances = ec2.run_instances(
            ImageId=self.image_id,
            InstanceType=self.server_type,
            MinCount=1,
            MaxCount=1,
            DisableApiTermination=True,
            SecurityGroupIds=[self.security_group_id],
            SubnetId=settings.TPSTREAMS_AWS_PROXY_SUBNET_ID,
            BlockDeviceMappings=[
                {
                    "DeviceName": "/dev/sda1",
                    "Ebs": {
                        "VolumeSize": self.volume_size,
                        "DeleteOnTermination": True,
                        "VolumeType": self.volume_type,
                    },
                }
            ],
            KeyName=settings.TPSTREAMS_AWS_PROXY_SERVER_KEY_PAIR_NAME,
            TagSpecifications=[
                {
                    "ResourceType": "instance",
                    "Tags": [{"Key": "Name", "Value": f"{name}"}],
                }
            ],
            UserData=user_data_script,
        )

        return self._parse_openresty_server_response(instances["Instances"][0])

    def _parse_openresty_server_response(self, data):
        server_id = data["InstanceId"]
        status = data["State"]["Name"]
        private_ip = data.get("NetworkInterfaces", [])[0].get("PrivateIpAddress")
        try:
            public_ip = (
                data.get("NetworkInterfaces", [])[0]
                .get("Association", {})
                .get("PublicIp")
            )
        except (AttributeError, IndexError):
            public_ip = None
        return {
            "instance_id": server_id,
            "state": status,
            "public_ip": public_ip,
            "private_ip": private_ip,
        }
