import json
from datetime import timed<PERSON><PERSON>

import sentry_sdk
from django.db import transaction
from django.db.models import Prefetch
from django.shortcuts import get_object_or_404
from django.utils.timezone import now
from knox.auth import TokenAuthentication
from rest_framework import generics, permissions, status, views
from rest_framework.authentication import SessionAuthentication
from rest_framework.response import Response

from app.api.authentication_classes import CsrfExemptSessionAuthentication
from app.api.mixins import OrganizationMixin
from app.api.v1.permissions import HasOrganizationAccess
from app.api.v1.permissions.live_stream import IsLiveStreamServerRequest
from app.api.v1.serializers import AssetSerializer
from app.api.v1.serializers.live_stream import (
    LiveStreamSerializer,
    LiveStreamTrimRequestSerializer,
)
from app.domain.live_stream import (
    create_chat_room_task,
    create_live_stream,
    create_live_stream_event,
    create_remote_live_stream_server,
    delete_live_stream_server,
    get_live_stream_drm_data,
    schedule_files_deletion,
    schedule_stop_disconnected_live_stream,
    stop_live_stream,
    store_live_stream_server_details,
    update_live_stream_server_ip_address_to_proxy,
    update_live_stream_server_status_as_creating,
    update_live_stream_status,
    update_live_stream_status_as_available,
    update_termination_log,
    upload_live_stream_recording,
)
from app.models import Asset, LiveStream, LiveStreamEvent, ScheduledTaskReference
from app.tasks.live_stream import StopDisconnectedLiveStreamTask


class LiveStreamListCreateView(OrganizationMixin, generics.ListCreateAPIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [permissions.IsAuthenticated, HasOrganizationAccess]
    serializer_class = AssetSerializer

    def post(self, request, organization_id):
        serializer = LiveStreamSerializer(data=request.data)
        if serializer.is_valid():
            validated_data = serializer.validated_data
            transcode_recorded_video = validated_data.get(
                "transcode_recorded_video", True
            )
            store_recorded_video = validated_data.get("store_recorded_video", True)
            if transcode_recorded_video:
                store_recorded_video = True
            live_stream = create_live_stream(
                validated_data["title"],
                self.organization,
                validated_data.get("enable_drm_for_recording", False),
                request.user,
                transcode_recorded_video=transcode_recorded_video,
                store_recorded_video=store_recorded_video,
                start=validated_data.get("start", now()),
                latency=validated_data.get(
                    "latency", LiveStream.Latency.NORMAL_LATENCY
                ),
            )
            create_chat_room_task(live_stream)
            response = AssetSerializer(live_stream.asset).data
            return Response(data=response, status=status.HTTP_201_CREATED)

        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_queryset(self):
        activities_prefetch = Prefetch(
            "live_stream__events",
            queryset=LiveStreamEvent.objects.filter(
                type__in=[
                    LiveStreamEvent.Type.ON_PUBISH,
                    LiveStreamEvent.Type.ON_PUBISH_DONE,
                    LiveStreamEvent.Type.STOPPED,
                ]
            ).only("type", "created"),
        )
        return Asset.objects.filter(live_stream__isnull=False).prefetch_related(
            "live_stream",
            "live_stream__organization",
            activities_prefetch,
        )


class CreateLiveStreamServerView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def post(self, request, *args, **kwargs):
        asset = get_object_or_404(
            Asset, uuid=kwargs["asset_id"], live_stream__isnull=False
        )

        with transaction.atomic():
            live_stream = LiveStream.objects.select_for_update().get(
                id=asset.live_stream.id
            )
            live_stream.refresh_from_db()

            if live_stream.server_status == LiveStream.ServerStatus.NOT_CREATED:
                update_live_stream_server_status_as_creating(live_stream)
                live_stream_server = create_remote_live_stream_server(asset)
                store_live_stream_server_details(live_stream, live_stream_server)

        return Response(
            AssetSerializer(asset).data,
            status=status.HTTP_200_OK,
        )


class LiveStreamServerStatusCallbackView(OrganizationMixin, generics.CreateAPIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsLiveStreamServerRequest]

    def post(self, request, *args, **kwargs):
        try:
            live_stream = LiveStream.objects.get(
                server_id=request.data.get("server_id")
            )
            if self.request.data.get("status") == "created":
                update_live_stream_server_ip_address_to_proxy(
                    live_stream, request.data.get("ip_address")
                )
                live_stream.asset.notify_webhook()
            elif self.request.data.get("status") == "ready":
                update_live_stream_status_as_available(live_stream)
            self.schedule_for_deletion_if_idle(live_stream)
            return Response(status=status.HTTP_200_OK)

        except LiveStream.DoesNotExist:
            sentry_sdk.capture_message(
                f"LiveStream not found for server_id: {request.data.get('server_id')}",
                level="error",
            )
            return Response(
                {"error": "LiveStream not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            sentry_sdk.capture_exception(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def schedule_for_deletion_if_idle(self, live_stream):
        live_stream_stop_schedule_time = 60  # After 60 minutes stop livestream
        if self.organization.uuid in ["bg5yak"]:
            live_stream_stop_schedule_time = 30

        task = StopDisconnectedLiveStreamTask.schedule(
            run_at=now() + timedelta(minutes=live_stream_stop_schedule_time),
            kwargs={"organization_uuid": self.organization.uuid, "live_stream_id": live_stream.id},  # type: ignore
            queue="live_stream",
        )
        live_stream.server_termination_task_id = task.task_id
        live_stream.save(update_fields=["server_termination_task_id"])
        create_live_stream_event(
            live_stream,
            LiveStreamEvent.Type.CREATED,
            {"message": (f"Scheduled termination task created: {task.task_id}")},
        )


class LiveStreamOnPublishCallbackView(OrganizationMixin, views.APIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsLiveStreamServerRequest]

    def post(self, request, *args, **kwargs):
        stream_key = request.data.get("stream_key")
        live_stream = get_object_or_404(
            LiveStream,
            stream_key=stream_key,
            status__in=[
                LiveStream.Status.NOT_STARTED,
                LiveStream.Status.STREAMING,
                LiveStream.Status.DISCONNECTED,
            ],
        )
        self.update_status_and_store_data(live_stream)
        self.revoke_server_deletion_scheduled_task(
            live_stream.server_termination_task_id
        )
        serializer = LiveStreamSerializer(live_stream)
        data = serializer.data
        data["drm"] = get_live_stream_drm_data(live_stream)
        live_stream.asset.notify_webhook()
        return Response(status=200, data=data)

    @transaction.atomic
    def update_status_and_store_data(self, live_stream):
        live_stream.status = LiveStream.Status.STREAMING
        live_stream.save(update_fields=["status"])
        LiveStreamEvent.objects.create(
            type=LiveStreamEvent.Type.ON_PUBISH,
            live_stream=live_stream,
            organization=self.organization,
            data=json.dumps(self.request.data),
        )

    def revoke_server_deletion_scheduled_task(self, task_id):
        if task_id:
            ScheduledTaskReference.objects.revoke(task_id=task_id)


class LiveStreamOnUnPublishCallbackView(OrganizationMixin, views.APIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsLiveStreamServerRequest]

    def post(self, request, *args, **kwargs):
        stream_key = request.data.get("stream_key")
        live_stream = get_object_or_404(
            LiveStream,
            stream_key=stream_key,
            status__in=[
                LiveStream.Status.STREAMING,
            ],
        )
        self.update_status_and_store_data(live_stream)
        schedule_stop_disconnected_live_stream(live_stream)
        live_stream.asset.notify_webhook()
        return Response(status=200)

    @transaction.atomic
    def update_status_and_store_data(self, live_stream):
        live_stream.status = LiveStream.Status.DISCONNECTED
        live_stream.save(update_fields=["status"])
        LiveStreamEvent.objects.create(
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            live_stream=live_stream,
            organization=self.organization,
            data=json.dumps(self.request.data),
        )


class StopLiveStreamView(OrganizationMixin, views.APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]

    def post(self, request, *args, **kwargs):
        asset = get_object_or_404(
            Asset, uuid=kwargs["asset_id"], live_stream__isnull=False
        )
        live_stream = asset.live_stream

        if live_stream.status in (
            LiveStream.Status.COMPLETED,
            LiveStream.Status.STOPPED,
            LiveStream.Status.ERROR,
        ):
            return Response(status=status.HTTP_204_NO_CONTENT)

        on_publish_event = (
            LiveStreamEvent.objects
            .filter(live_stream=live_stream, type=LiveStreamEvent.Type.ON_PUBISH)
            .order_by("created")
            .first()
        )
        if on_publish_event:
            duration = int((now() - on_publish_event.created).total_seconds())
        else:
            duration = None

        is_trim_scheduled = False

        if request.data and hasattr(asset, "live_stream"):
            serializer = LiveStreamTrimRequestSerializer(
                data=request.data, duration=duration, live_stream=asset.live_stream
            )
            serializer.is_valid(raise_exception=True)

            start_time = serializer.validated_data.get("start_time")
            end_time = serializer.validated_data.get("end_time")

            if start_time is not None or end_time is not None:
                is_trim_scheduled = self._store_trim_intent(
                    asset.live_stream, start_time, end_time, request.user
                )

        response_data["trim_data"] = serializer.validated_data
        stop_live_stream(asset.live_stream)
        update_termination_log(asset.live_stream, request)

        response_data = {"message": "Live stream stopped successfully"}
        response_data["trim_scheduled"] = is_trim_scheduled

        return Response(response_data, status=200)

    def _store_trim_intent(self, live_stream, start_time, end_time, user):
        live_stream.scheduled_trim_data = {
            "start_time": start_time,
            "end_time": end_time,
            "user_id": user.id,
        }
        live_stream.save(update_fields=["scheduled_trim_data"])
        return True


class LiveStreamOnUploadCompleteCallbackView(OrganizationMixin, views.APIView):
    authentication_classes = [CsrfExemptSessionAuthentication]
    permission_classes = [IsLiveStreamServerRequest]

    def post(self, request, *args, **kwargs):
        stream_key = request.data.get("stream_key")
        live_stream = get_object_or_404(
            LiveStream,
            stream_key=stream_key,
            status__in=[
                LiveStream.Status.STOPPED,
            ],
        )
        if live_stream.store_recorded_video:
            upload_live_stream_recording(live_stream)
        else:
            delete_live_stream_server(live_stream)
            update_live_stream_status(live_stream, LiveStream.Status.COMPLETED)
            create_live_stream_event(
                live_stream,
                LiveStreamEvent.Type.COMPLETED,
                {
                    "message": (
                        "Recording upload skipped as 'store_recorded_video' is disabled. "
                        "Live stream marked as completed."
                    )
                },
            )

        schedule_files_deletion(live_stream)
        return Response(status=200)
