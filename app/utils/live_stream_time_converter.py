from datetime import datetime

import pytz

from app.utils.hls_trimmer import get_hls_stream_start_time, HLSTrimmerError


class LiveStreamTimeConversionError(Exception):
    pass


def is_epoch_timestamp(timestamp: int):
    # Epoch timestamp threshold: January 1, 2001 (946684800)
    EPOCH_THRESHOLD = 946684800
    return timestamp > EPOCH_THRESHOLD


def convert_epoch_to_relative_seconds(
    epoch_timestamp, 
    hls_manifest_url
):
    try:
        stream_start_time = get_hls_stream_start_time(hls_manifest_url)
        
        if stream_start_time is None:
            raise LiveStreamTimeConversionError(
                "Could not extract stream start time from HLS manifest. "
                "The manifest may not contain #EXT-X-PROGRAM-DATE-TIME tags."
            )

        epoch_datetime = datetime.fromtimestamp(epoch_timestamp, tz=pytz.UTC)

        time_diff = epoch_datetime - stream_start_time
        relative_seconds = int(time_diff.total_seconds())
        
        if relative_seconds < 0:
            raise LiveStreamTimeConversionError(
                f"Epoch timestamp ({epoch_timestamp}) is before stream start time "
                f"({stream_start_time.isoformat()})"
            )
        
        return relative_seconds
        
    except HLSTrimmerError as e:
        raise LiveStreamTimeConversionError(f"HLS manifest parsing failed: {str(e)}") from e
    except Exception as e:
        raise LiveStreamTimeConversionError(f"Time conversion failed: {str(e)}") from e


def convert_timestamps_if_needed(
    start_time,
    end_time,
    hls_manifest_url
):
    using_epoch = False
    if start_time is not None:
        using_epoch = is_epoch_timestamp(start_time)
    elif end_time is not None:
        using_epoch = is_epoch_timestamp(end_time)

    if using_epoch:
        converted_start_time = None
        converted_end_time = None

        if start_time is not None:
            converted_start_time = convert_epoch_to_relative_seconds(start_time, hls_manifest_url)

        if end_time is not None:
            converted_end_time = convert_epoch_to_relative_seconds(end_time, hls_manifest_url)

        return converted_start_time, converted_end_time
    else:
        return start_time, end_time


def detect_timestamp_types(
    start_time,
    end_time
):
    start_is_epoch = start_time is not None and is_epoch_timestamp(start_time)
    end_is_epoch = end_time is not None and is_epoch_timestamp(end_time)
    
    return start_is_epoch, end_is_epoch
