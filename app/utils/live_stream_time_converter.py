"""
Utility functions for converting between epoch timestamps and relative seconds
in live stream trimming operations.
"""
from datetime import datetime
from typing import Optional, <PERSON>ple

import pytz

from app.utils.hls_trimmer import get_hls_stream_start_time, HLSTrimmerError


class LiveStreamTimeConversionError(Exception):
    pass


def is_epoch_timestamp(timestamp: int):
    # Epoch timestamp threshold: January 1, 2001 (946684800)
    EPOCH_THRESHOLD = 946684800
    return timestamp > EPOCH_THRESHOLD


def convert_epoch_to_relative_seconds(
    epoch_timestamp, 
    hls_manifest_url
):
    """
    Convert an epoch timestamp to relative seconds from the start of a live stream.
    
    Args:
        epoch_timestamp (int): Unix epoch timestamp
        hls_manifest_url (str): URL to the HLS master manifest
        
    Returns:
        int: Relative seconds from the start of the stream
        
    Raises:
        LiveStreamTimeConversionError: If conversion fails
    """
    try:
        # Get the stream start time from the HLS manifest
        stream_start_time = get_hls_stream_start_time(hls_manifest_url)
        
        if stream_start_time is None:
            raise LiveStreamTimeConversionError(
                "Could not extract stream start time from HLS manifest. "
                "The manifest may not contain #EXT-X-PROGRAM-DATE-TIME tags."
            )
        
        # Convert epoch timestamp to datetime
        epoch_datetime = datetime.fromtimestamp(epoch_timestamp, tz=pytz.UTC)
        
        # Calculate relative seconds
        time_diff = epoch_datetime - stream_start_time
        relative_seconds = int(time_diff.total_seconds())
        
        if relative_seconds < 0:
            raise LiveStreamTimeConversionError(
                f"Epoch timestamp ({epoch_timestamp}) is before stream start time "
                f"({stream_start_time.isoformat()})"
            )
        
        return relative_seconds
        
    except HLSTrimmerError as e:
        raise LiveStreamTimeConversionError(f"HLS manifest parsing failed: {str(e)}") from e
    except Exception as e:
        raise LiveStreamTimeConversionError(f"Time conversion failed: {str(e)}") from e


def convert_timestamps_if_needed(
    start_time: Optional[int],
    end_time: Optional[int],
    hls_manifest_url: str
) -> Tuple[Optional[int], Optional[int]]:
    """
    Convert timestamps from epoch to relative seconds if they appear to be epoch timestamps.
    Note: This function assumes all provided timestamps are of the same type (all epoch or all relative).

    Args:
        start_time (Optional[int]): Start time (epoch or relative seconds)
        end_time (Optional[int]): End time (epoch or relative seconds)
        hls_manifest_url (str): URL to the HLS master manifest

    Returns:
        Tuple[Optional[int], Optional[int]]: Converted start_time and end_time

    Raises:
        LiveStreamTimeConversionError: If conversion fails
    """
    # Determine if we're dealing with epoch timestamps by checking any non-None value
    using_epoch = False
    if start_time is not None:
        using_epoch = is_epoch_timestamp(start_time)
    elif end_time is not None:
        using_epoch = is_epoch_timestamp(end_time)

    # If using epoch timestamps, convert both (if they exist)
    if using_epoch:
        converted_start_time = None
        converted_end_time = None

        if start_time is not None:
            converted_start_time = convert_epoch_to_relative_seconds(start_time, hls_manifest_url)

        if end_time is not None:
            converted_end_time = convert_epoch_to_relative_seconds(end_time, hls_manifest_url)

        return converted_start_time, converted_end_time
    else:
        # No conversion needed for relative seconds
        return start_time, end_time


def detect_timestamp_types(
    start_time: Optional[int],
    end_time: Optional[int]
) -> Tuple[bool, bool]:
    """
    Detect whether start_time and end_time are epoch timestamps or relative seconds.
    
    Args:
        start_time (Optional[int]): Start time to check
        end_time (Optional[int]): End time to check
        
    Returns:
        Tuple[bool, bool]: (start_is_epoch, end_is_epoch)
    """
    start_is_epoch = start_time is not None and is_epoch_timestamp(start_time)
    end_is_epoch = end_time is not None and is_epoch_timestamp(end_time)
    
    return start_is_epoch, end_is_epoch
