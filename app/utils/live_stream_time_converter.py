"""
Utility functions for converting between epoch timestamps and relative seconds
in live stream trimming operations.
"""
from datetime import datetime
from typing import Optional, Tuple

import pytz

from app.utils.hls_trimmer import get_hls_stream_start_time, HLSTrimmerError


class LiveStreamTimeConversionError(Exception):
    """Exception raised when time conversion fails."""
    pass


def is_epoch_timestamp(timestamp: int) -> bool:
    """
    Determine if a timestamp is an epoch timestamp or relative seconds.
    
    Epoch timestamps are typically large numbers (> 1000000000, which is ~2001).
    Relative seconds for live streams are typically much smaller (< 86400 for a day).
    
    Args:
        timestamp (int): The timestamp to check
        
    Returns:
        bool: True if the timestamp appears to be an epoch timestamp
    """
    # Epoch timestamp threshold: January 1, 2001 (946684800)
    # This gives us a reasonable cutoff between epoch and relative seconds
    EPOCH_THRESHOLD = 946684800
    return timestamp > EPOCH_THRESHOLD


def convert_epoch_to_relative_seconds(
    epoch_timestamp: int, 
    hls_manifest_url: str
) -> int:
    """
    Convert an epoch timestamp to relative seconds from the start of a live stream.
    
    Args:
        epoch_timestamp (int): Unix epoch timestamp
        hls_manifest_url (str): URL to the HLS master manifest
        
    Returns:
        int: Relative seconds from the start of the stream
        
    Raises:
        LiveStreamTimeConversionError: If conversion fails
    """
    try:
        # Get the stream start time from the HLS manifest
        stream_start_time = get_hls_stream_start_time(hls_manifest_url)
        
        if stream_start_time is None:
            raise LiveStreamTimeConversionError(
                "Could not extract stream start time from HLS manifest. "
                "The manifest may not contain #EXT-X-PROGRAM-DATE-TIME tags."
            )
        
        # Convert epoch timestamp to datetime
        epoch_datetime = datetime.fromtimestamp(epoch_timestamp, tz=pytz.UTC)
        
        # Calculate relative seconds
        time_diff = epoch_datetime - stream_start_time
        relative_seconds = int(time_diff.total_seconds())
        
        if relative_seconds < 0:
            raise LiveStreamTimeConversionError(
                f"Epoch timestamp ({epoch_timestamp}) is before stream start time "
                f"({stream_start_time.isoformat()})"
            )
        
        return relative_seconds
        
    except HLSTrimmerError as e:
        raise LiveStreamTimeConversionError(f"HLS manifest parsing failed: {str(e)}") from e
    except Exception as e:
        raise LiveStreamTimeConversionError(f"Time conversion failed: {str(e)}") from e


def convert_timestamps_if_needed(
    start_time: Optional[int],
    end_time: Optional[int],
    hls_manifest_url: str
) -> Tuple[Optional[int], Optional[int]]:
    """
    Convert timestamps from epoch to relative seconds if they appear to be epoch timestamps.
    
    Args:
        start_time (Optional[int]): Start time (epoch or relative seconds)
        end_time (Optional[int]): End time (epoch or relative seconds)
        hls_manifest_url (str): URL to the HLS master manifest
        
    Returns:
        Tuple[Optional[int], Optional[int]]: Converted start_time and end_time
        
    Raises:
        LiveStreamTimeConversionError: If conversion fails
    """
    converted_start_time = start_time
    converted_end_time = end_time
    
    # Check if we need to convert start_time
    if start_time is not None and is_epoch_timestamp(start_time):
        converted_start_time = convert_epoch_to_relative_seconds(start_time, hls_manifest_url)
    
    # Check if we need to convert end_time
    if end_time is not None and is_epoch_timestamp(end_time):
        converted_end_time = convert_epoch_to_relative_seconds(end_time, hls_manifest_url)
    
    return converted_start_time, converted_end_time


def detect_timestamp_types(
    start_time: Optional[int],
    end_time: Optional[int]
) -> Tuple[bool, bool]:
    """
    Detect whether start_time and end_time are epoch timestamps or relative seconds.
    
    Args:
        start_time (Optional[int]): Start time to check
        end_time (Optional[int]): End time to check
        
    Returns:
        Tuple[bool, bool]: (start_is_epoch, end_is_epoch)
    """
    start_is_epoch = start_time is not None and is_epoch_timestamp(start_time)
    end_is_epoch = end_time is not None and is_epoch_timestamp(end_time)
    
    return start_is_epoch, end_is_epoch
