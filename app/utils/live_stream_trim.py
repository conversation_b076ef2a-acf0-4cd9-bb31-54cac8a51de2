import re
from datetime import datetime, timedelta
from typing import Optional, <PERSON><PERSON>

import m3u8
from django.utils.timezone import now

from app.utils.m3u8 import get_m3u8_content


class LiveStreamTrimError(Exception):
    """Exception raised for errors in live stream trimming operations."""
    pass


def is_epoch_timestamp(timestamp: int) -> bool:
    """
    Determine if a timestamp is likely an epoch timestamp (Unix timestamp).
    
    Args:
        timestamp: The timestamp to check
        
    Returns:
        bool: True if the timestamp appears to be an epoch timestamp
    """
    current_time = int(now().timestamp())
    
    # If timestamp is within the last 10 years or next 10 years, it's likely epoch
    # This covers timestamps from 2014 to 2034
    min_epoch = current_time - (10 * 365 * 24 * 3600)  # 10 years ago
    max_epoch = current_time + (10 * 365 * 24 * 3600)  # 10 years from now
    
    return min_epoch <= timestamp <= max_epoch


def parse_hls_manifest_for_program_datetime(hls_url: str) -> Optional[datetime]:
    """
    Parse HLS manifest to extract the first EXT-X-PROGRAM-DATE-TIME tag.
    
    Args:
        hls_url: URL to the HLS manifest
        
    Returns:
        Optional[datetime]: The program date time if found, None otherwise
    """
    try:
        manifest_content = get_m3u8_content(hls_url)
        if not manifest_content:
            return None
            
        playlist = m3u8.loads(manifest_content)
        
        # Look for EXT-X-PROGRAM-DATE-TIME in the manifest
        for segment in playlist.segments:
            if hasattr(segment, 'program_date_time') and segment.program_date_time:
                return segment.program_date_time
                
        # If not found in segments, check the manifest content directly
        lines = manifest_content.split('\n')
        for line in lines:
            if line.startswith('#EXT-X-PROGRAM-DATE-TIME:'):
                # Extract the datetime string after the colon
                datetime_str = line.split(':', 1)[1].strip()
                try:
                    # Parse ISO 8601 format (e.g., "2024-01-15T10:30:00.000Z")
                    return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
                except ValueError:
                    # Try alternative formats if ISO format fails
                    try:
                        return datetime.strptime(datetime_str, '%Y-%m-%dT%H:%M:%S.%fZ')
                    except ValueError:
                        try:
                            return datetime.strptime(datetime_str, '%Y-%m-%dT%H:%M:%SZ')
                        except ValueError:
                            continue
                            
        return None
        
    except Exception as e:
        raise LiveStreamTrimError(f"Failed to parse HLS manifest: {str(e)}") from e


def convert_epoch_to_video_seconds(
    epoch_timestamp: int, 
    program_datetime: datetime, 
    video_duration: int
) -> int:
    """
    Convert an epoch timestamp to video seconds based on program datetime.
    
    Args:
        epoch_timestamp: Unix timestamp to convert
        program_datetime: Program date time from HLS manifest
        video_duration: Total video duration in seconds
        
    Returns:
        int: Video seconds corresponding to the epoch timestamp
    """
    try:
        # Convert epoch to datetime
        epoch_datetime = datetime.fromtimestamp(epoch_timestamp)
        
        # Calculate time difference from program start
        time_diff = epoch_datetime - program_datetime
        
        # Convert to seconds
        video_seconds = int(time_diff.total_seconds())
        
        # Ensure the result is within valid video duration
        if video_seconds < 0:
            return 0
        elif video_seconds > video_duration:
            return video_duration
            
        return video_seconds
        
    except Exception as e:
        raise LiveStreamTrimError(f"Failed to convert epoch to video seconds: {str(e)}") from e


def convert_video_seconds_to_epoch(
    video_seconds: int, 
    program_datetime: datetime
) -> int:
    """
    Convert video seconds to epoch timestamp based on program datetime.
    
    Args:
        video_seconds: Video seconds to convert
        program_datetime: Program date time from HLS manifest
        
    Returns:
        int: Epoch timestamp corresponding to the video seconds
    """
    try:
        # Add video seconds to program datetime
        target_datetime = program_datetime + timedelta(seconds=video_seconds)
        
        # Convert to epoch timestamp
        return int(target_datetime.timestamp())
        
    except Exception as e:
        raise LiveStreamTrimError(f"Failed to convert video seconds to epoch: {str(e)}") from e


def process_trim_timestamps(
    start_time: Optional[int], 
    end_time: Optional[int], 
    hls_url: str, 
    video_duration: int
) -> Tuple[Optional[int], Optional[int]]:
    """
    Process trim timestamps, converting epoch timestamps to video seconds if needed.
    
    Args:
        start_time: Start time (epoch or video seconds)
        end_time: End time (epoch or video seconds)
        hls_url: URL to the HLS manifest
        video_duration: Total video duration in seconds
        
    Returns:
        Tuple[Optional[int], Optional[int]]: Processed start_time and end_time in video seconds
    """
    processed_start_time = start_time
    processed_end_time = end_time
    
    # Check if we need to process epoch timestamps
    needs_epoch_processing = False
    if start_time is not None and is_epoch_timestamp(start_time):
        needs_epoch_processing = True
    if end_time is not None and is_epoch_timestamp(end_time):
        needs_epoch_processing = True
    
    if needs_epoch_processing:
        # Get program datetime from HLS manifest
        program_datetime = parse_hls_manifest_for_program_datetime(hls_url)
        if not program_datetime:
            raise LiveStreamTrimError(
                "Cannot process epoch timestamps: EXT-X-PROGRAM-DATE-TIME not found in HLS manifest"
            )
        
        # Convert epoch timestamps to video seconds
        if start_time is not None and is_epoch_timestamp(start_time):
            processed_start_time = convert_epoch_to_video_seconds(
                start_time, program_datetime, video_duration
            )
            
        if end_time is not None and is_epoch_timestamp(end_time):
            processed_end_time = convert_epoch_to_video_seconds(
                end_time, program_datetime, video_duration
            )
    
    return processed_start_time, processed_end_time


def validate_trim_timestamps(
    start_time: Optional[int], 
    end_time: Optional[int], 
    video_duration: int
) -> None:
    """
    Validate trim timestamps are within valid range.
    
    Args:
        start_time: Start time in video seconds
        end_time: End time in video seconds
        video_duration: Total video duration in seconds
        
    Raises:
        LiveStreamTrimError: If timestamps are invalid
    """
    if start_time is not None:
        if start_time < 0:
            raise LiveStreamTrimError("Start time cannot be negative")
        if start_time >= video_duration:
            raise LiveStreamTrimError("Start time cannot exceed video duration")
    
    if end_time is not None:
        if end_time < 0:
            raise LiveStreamTrimError("End time cannot be negative")
        if end_time > video_duration:
            raise LiveStreamTrimError("End time cannot exceed video duration")
    
    if start_time is not None and end_time is not None:
        if start_time >= end_time:
            raise LiveStreamTrimError("Start time must be less than end time") 