"""
Tests for HLS manifest parsing functionality for extracting program date time.
"""
from datetime import datetime
from unittest.mock import patch, MagicMock

import pytz
from django.test import TestCase

from app.utils.hls_trimmer import (
    get_hls_stream_start_time,
    _extract_program_date_time_from_playlist,
    HLSTrimmerError,
)


class TestHLSManifestParsing(TestCase):
    """Test HLS manifest parsing for program date time extraction."""

    def setUp(self):
        """Set up test fixtures."""
        self.master_manifest_url = "http://example.com/live/org/asset/video.m3u8"
        
        # Sample master manifest content
        self.master_manifest_content = """#EXTM3U
#EXT-X-VERSION:3
#EXT-X-STREAM-INF:BANDWIDTH=1000000,RESOLUTION=1280x720
720p/video.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=500000,RESOLUTION=854x480
480p/video.m3u8
"""
        
        # Sample variant manifest with program date time
        self.variant_manifest_content = """#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-MEDIA-SEQUENCE:0
#EXT-X-PROGRAM-DATE-TIME:2023-01-01T10:00:00.000Z
#EXTINF:10.0,
segment0.ts
#EXTINF:10.0,
segment1.ts
#EXT-X-ENDLIST
"""

    @patch('app.utils.hls_trimmer.get_m3u8_content')
    @patch('app.utils.hls_trimmer.m3u8.loads')
    def test_get_stream_start_time_from_variant(self, mock_m3u8_loads, mock_get_content):
        """Test extracting stream start time from variant playlist."""
        # Mock master playlist
        mock_master_playlist = MagicMock()
        mock_master_playlist.segments = []  # No direct segments
        mock_variant = MagicMock()
        mock_variant.uri = "720p/video.m3u8"
        mock_master_playlist.playlists = [mock_variant]
        
        # Mock variant playlist with program date time
        mock_variant_playlist = MagicMock()
        mock_segment = MagicMock()
        mock_segment.program_date_time = "2023-01-01T10:00:00.000Z"
        mock_variant_playlist.segments = [mock_segment]
        
        # Configure mocks
        mock_m3u8_loads.side_effect = [mock_master_playlist, mock_variant_playlist]
        mock_get_content.side_effect = [
            self.master_manifest_content,  # Master manifest
            self.variant_manifest_content   # Variant manifest
        ]
        
        result = get_hls_stream_start_time(self.master_manifest_url)
        
        expected_time = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(result, expected_time)

    @patch('app.utils.hls_trimmer.get_m3u8_content')
    def test_get_stream_start_time_master_manifest_not_found(self, mock_get_content):
        """Test error when master manifest cannot be fetched."""
        mock_get_content.return_value = None
        
        with self.assertRaises(HLSTrimmerError) as context:
            get_hls_stream_start_time(self.master_manifest_url)
        
        self.assertIn("Could not fetch master manifest", str(context.exception))

    @patch('app.utils.hls_trimmer.get_m3u8_content')
    @patch('app.utils.hls_trimmer.m3u8.loads')
    def test_get_stream_start_time_no_variants(self, mock_m3u8_loads, mock_get_content):
        """Test error when no variant playlists are found."""
        mock_master_playlist = MagicMock()
        mock_master_playlist.segments = []
        mock_master_playlist.playlists = []
        
        mock_m3u8_loads.return_value = mock_master_playlist
        mock_get_content.return_value = self.master_manifest_content
        
        with self.assertRaises(HLSTrimmerError) as context:
            get_hls_stream_start_time(self.master_manifest_url)
        
        self.assertIn("No variant playlists found", str(context.exception))

    @patch('app.utils.hls_trimmer.get_m3u8_content')
    @patch('app.utils.hls_trimmer.m3u8.loads')
    def test_get_stream_start_time_no_program_date_time(self, mock_m3u8_loads, mock_get_content):
        """Test when no program date time is found in any variant."""
        # Mock master playlist
        mock_master_playlist = MagicMock()
        mock_master_playlist.segments = []
        mock_variant = MagicMock()
        mock_variant.uri = "720p/video.m3u8"
        mock_master_playlist.playlists = [mock_variant]
        
        # Mock variant playlist without program date time
        mock_variant_playlist = MagicMock()
        mock_segment = MagicMock()
        mock_segment.program_date_time = None
        mock_variant_playlist.segments = [mock_segment]
        
        mock_m3u8_loads.side_effect = [mock_master_playlist, mock_variant_playlist]
        mock_get_content.side_effect = [
            self.master_manifest_content,
            self.variant_manifest_content
        ]
        
        result = get_hls_stream_start_time(self.master_manifest_url)
        self.assertIsNone(result)

    def test_extract_program_date_time_from_playlist(self):
        """Test extracting program date time from playlist object."""
        # Mock playlist with program date time
        mock_playlist = MagicMock()
        mock_segment = MagicMock()
        mock_segment.program_date_time = "2023-01-01T10:00:00.000Z"
        mock_playlist.segments = [mock_segment]
        
        result = _extract_program_date_time_from_playlist(mock_playlist)
        
        expected_time = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(result, expected_time)

    def test_extract_program_date_time_no_segments(self):
        """Test extracting program date time when no segments exist."""
        mock_playlist = MagicMock()
        mock_playlist.segments = []
        
        result = _extract_program_date_time_from_playlist(mock_playlist)
        self.assertIsNone(result)

    def test_extract_program_date_time_different_formats(self):
        """Test extracting program date time with different datetime formats."""
        test_cases = [
            ("2023-01-01T10:00:00.000Z", datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)),
            ("2023-01-01T10:00:00Z", datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)),
            ("2023-01-01T10:00:00+00:00", datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)),
        ]
        
        for date_string, expected_time in test_cases:
            with self.subTest(date_string=date_string):
                mock_playlist = MagicMock()
                mock_segment = MagicMock()
                mock_segment.program_date_time = date_string
                mock_playlist.segments = [mock_segment]
                
                result = _extract_program_date_time_from_playlist(mock_playlist)
                self.assertEqual(result, expected_time)

    def test_extract_program_date_time_invalid_format(self):
        """Test extracting program date time with invalid format."""
        mock_playlist = MagicMock()
        mock_segment1 = MagicMock()
        mock_segment1.program_date_time = "invalid-date-format"
        mock_segment2 = MagicMock()
        mock_segment2.program_date_time = "2023-01-01T10:00:00.000Z"
        mock_playlist.segments = [mock_segment1, mock_segment2]
        
        # Should skip invalid format and return the valid one
        result = _extract_program_date_time_from_playlist(mock_playlist)
        expected_time = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(result, expected_time)

    @patch('app.utils.hls_trimmer.get_m3u8_content')
    @patch('app.utils.hls_trimmer.m3u8.loads')
    def test_get_stream_start_time_direct_segments(self, mock_m3u8_loads, mock_get_content):
        """Test extracting stream start time from master playlist with direct segments."""
        # Mock master playlist with direct segments
        mock_master_playlist = MagicMock()
        mock_segment = MagicMock()
        mock_segment.program_date_time = "2023-01-01T10:00:00.000Z"
        mock_master_playlist.segments = [mock_segment]
        
        mock_m3u8_loads.return_value = mock_master_playlist
        mock_get_content.return_value = self.master_manifest_content
        
        result = get_hls_stream_start_time(self.master_manifest_url)
        
        expected_time = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        self.assertEqual(result, expected_time)
