"""
Integration tests for StopLiveStreamView with epoch timestamp support.
"""
from datetime import datetime
from unittest.mock import patch, MagicMock

import pytz
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from app.models import Asset, LiveStream, LiveStreamEvent, Organization
from app.utils.live_stream_time_converter import LiveStreamTimeConversionError

User = get_user_model()


class TestStopLiveStreamViewEpochSupport(TestCase):
    """Integration tests for StopLiveStreamView with epoch timestamp support."""

    def setUp(self):
        """Set up test fixtures."""
        self.client = APIClient()
        
        # Create test organization and user
        self.organization = Organization.objects.create(
            name="Test Org",
            uuid="test-org-uuid",
            cdn_url="https://cdn.example.com/"
        )
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )
        
        # Create test asset and live stream
        self.asset = Asset.objects.create(
            title="Test Live Stream",
            organization=self.organization,
            created_by=self.user,
            type=Asset.Type.LIVESTREAM
        )
        self.live_stream = LiveStream.objects.create(
            asset=self.asset,
            organization=self.organization,
            rtmp_url="rtmp://example.com/live",
            hls_url_path="live/test-org-uuid/test-asset-uuid/video.m3u8",
            status=LiveStream.Status.STREAMING
        )
        
        # Create ON_PUBLISH event for duration calculation
        self.on_publish_event = LiveStreamEvent.objects.create(
            type=LiveStreamEvent.Type.ON_PUBISH,
            live_stream=self.live_stream,
            organization=self.organization
        )
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
        
        self.url = reverse(
            'api:stop-live-stream',
            kwargs={
                'organization_id': self.organization.uuid,
                'asset_id': self.asset.uuid
            }
        )

    def test_stop_live_stream_with_relative_seconds(self):
        """Test stopping live stream with relative seconds (existing behavior)."""
        data = {
            "start_time": 300,  # 5 minutes
            "end_time": 600     # 10 minutes
        }
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['trim_scheduled'])
        
        # Check that trim data was stored
        self.live_stream.refresh_from_db()
        trim_data = self.live_stream.scheduled_trim_data
        self.assertEqual(trim_data['start_time'], 300)
        self.assertEqual(trim_data['end_time'], 600)
        self.assertEqual(trim_data['user_id'], self.user.id)

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_stop_live_stream_with_epoch_timestamps(self, mock_get_start_time):
        """Test stopping live stream with epoch timestamps."""
        # Mock stream start time: 2023-01-01 10:00:00 UTC
        stream_start = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        mock_get_start_time.return_value = stream_start
        
        data = {
            "start_time": 1672574700,  # 2023-01-01 10:05:00 UTC (5 minutes after start)
            "end_time": 1672575000     # 2023-01-01 10:10:00 UTC (10 minutes after start)
        }
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['trim_scheduled'])
        
        # Check that trim data was stored with converted relative seconds
        self.live_stream.refresh_from_db()
        trim_data = self.live_stream.scheduled_trim_data
        self.assertEqual(trim_data['start_time'], 300)  # 5 minutes in seconds
        self.assertEqual(trim_data['end_time'], 600)    # 10 minutes in seconds
        self.assertEqual(trim_data['user_id'], self.user.id)
        
        # Verify HLS manifest URL was used for conversion
        expected_hls_url = f"{self.organization.cdn_url}{self.live_stream.hls_url_path}"
        mock_get_start_time.assert_called_once_with(expected_hls_url)

    def test_stop_live_stream_mixed_timestamp_types_not_allowed(self):
        """Test that mixed timestamp types are not allowed."""
        data = {
            "start_time": 1672574700,  # Epoch timestamp
            "end_time": 600            # Relative seconds
        }

        response = self.client.post(self.url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Cannot mix epoch timestamps and relative seconds", str(response.data))

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_stop_live_stream_epoch_conversion_error(self, mock_get_start_time):
        """Test error handling when epoch conversion fails."""
        mock_get_start_time.return_value = None  # No program date time found
        
        data = {
            "start_time": 1672574700,  # Epoch timestamp
            "end_time": 600
        }
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Time conversion failed", str(response.data))

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_stop_live_stream_epoch_out_of_range(self, mock_get_start_time):
        """Test error when converted epoch timestamp is out of range."""
        # Mock stream start time much later than the epoch timestamp
        stream_start = datetime(2023, 1, 1, 12, 0, 0, tzinfo=pytz.UTC)  # 2 hours later
        mock_get_start_time.return_value = stream_start
        
        data = {
            "start_time": 1672574700,  # 2023-01-01 10:05:00 UTC (before stream start)
            "end_time": 600
        }
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Time conversion failed", str(response.data))

    def test_stop_live_stream_no_trim_data(self):
        """Test stopping live stream without trim data."""
        response = self.client.post(self.url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['trim_scheduled'])
        
        # Check that no trim data was stored
        self.live_stream.refresh_from_db()
        self.assertIsNone(self.live_stream.scheduled_trim_data)

    def test_stop_live_stream_already_stopped(self):
        """Test stopping an already stopped live stream."""
        self.live_stream.status = LiveStream.Status.STOPPED
        self.live_stream.save()
        
        data = {"start_time": 300, "end_time": 600}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_stop_live_stream_invalid_asset(self):
        """Test stopping live stream with invalid asset ID."""
        invalid_url = reverse(
            'api:stop-live-stream',
            kwargs={
                'organization_id': self.organization.uuid,
                'asset_id': 'invalid-uuid'
            }
        )
        
        data = {"start_time": 300, "end_time": 600}
        response = self.client.post(invalid_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_stop_live_stream_unauthenticated(self):
        """Test stopping live stream without authentication."""
        self.client.force_authenticate(user=None)
        
        data = {"start_time": 300, "end_time": 600}
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
