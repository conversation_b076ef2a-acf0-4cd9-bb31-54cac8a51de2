"""
Tests for live stream time conversion utilities.
"""
from datetime import datetime
from unittest.mock import patch, MagicMock

import pytz
from django.test import TestCase

from app.utils.live_stream_time_converter import (
    is_epoch_timestamp,
    convert_epoch_to_relative_seconds,
    convert_timestamps_if_needed,
    detect_timestamp_types,
    LiveStreamTimeConversionError,
)
from app.utils.hls_trimmer import HLSTrimmerError


class TestIsEpochTimestamp(TestCase):
    """Test epoch timestamp detection."""

    def test_epoch_timestamp_detection(self):
        """Test that epoch timestamps are correctly identified."""
        # Epoch timestamps (large numbers)
        self.assertTrue(is_epoch_timestamp(1640995200))  # 2022-01-01 00:00:00 UTC
        self.assertTrue(is_epoch_timestamp(1672531200))  # 2023-01-01 00:00:00 UTC
        self.assertTrue(is_epoch_timestamp(1000000000))  # 2001-09-09 01:46:40 UTC

    def test_relative_seconds_detection(self):
        """Test that relative seconds are correctly identified."""
        # Relative seconds (small numbers)
        self.assertFalse(is_epoch_timestamp(0))
        self.assertFalse(is_epoch_timestamp(30))
        self.assertFalse(is_epoch_timestamp(3600))  # 1 hour
        self.assertFalse(is_epoch_timestamp(86400))  # 1 day
        self.assertFalse(is_epoch_timestamp(946684799))  # Just below threshold


class TestConvertEpochToRelativeSeconds(TestCase):
    """Test epoch to relative seconds conversion."""

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_successful_conversion(self, mock_get_start_time):
        """Test successful conversion of epoch timestamp to relative seconds."""
        # Mock stream start time: 2023-01-01 10:00:00 UTC
        stream_start = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        mock_get_start_time.return_value = stream_start

        # Epoch timestamp: 2023-01-01 10:05:00 UTC (5 minutes later)
        # 1672574700 = 2023-01-01 10:05:00 UTC
        epoch_timestamp = 1672574700

        result = convert_epoch_to_relative_seconds(epoch_timestamp, "http://example.com/playlist.m3u8")

        # Calculate expected result: epoch_timestamp - stream_start_timestamp
        # stream_start = 2023-01-01 10:00:00 UTC = 1672574400
        # epoch_timestamp = 1672574700
        # difference = 1672574700 - 1672574400 = 300 seconds
        expected_result = epoch_timestamp - int(stream_start.timestamp())
        self.assertEqual(result, expected_result)
        mock_get_start_time.assert_called_once_with("http://example.com/playlist.m3u8")

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_conversion_with_no_program_date_time(self, mock_get_start_time):
        """Test conversion fails when no program date time is found."""
        mock_get_start_time.return_value = None
        
        with self.assertRaises(LiveStreamTimeConversionError) as context:
            convert_epoch_to_relative_seconds(1672574700, "http://example.com/playlist.m3u8")
        
        self.assertIn("Could not extract stream start time", str(context.exception))

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_conversion_with_timestamp_before_stream_start(self, mock_get_start_time):
        """Test conversion fails when epoch timestamp is before stream start."""
        # Stream start: 2023-01-01 10:00:00 UTC
        stream_start = datetime(2023, 1, 1, 10, 0, 0, tzinfo=pytz.UTC)
        mock_get_start_time.return_value = stream_start

        # Epoch timestamp that's before stream start
        # stream_start.timestamp() = 1672567200, so use something smaller
        epoch_timestamp = 1672563600  # 2023-01-01 09:00:00 UTC (1 hour before)

        with self.assertRaises(LiveStreamTimeConversionError) as context:
            convert_epoch_to_relative_seconds(epoch_timestamp, "http://example.com/playlist.m3u8")

        self.assertIn("is before stream start time", str(context.exception))

    @patch('app.utils.live_stream_time_converter.get_hls_stream_start_time')
    def test_conversion_with_hls_error(self, mock_get_start_time):
        """Test conversion fails when HLS parsing fails."""
        mock_get_start_time.side_effect = HLSTrimmerError("Manifest parsing failed")
        
        with self.assertRaises(LiveStreamTimeConversionError) as context:
            convert_epoch_to_relative_seconds(1672574700, "http://example.com/playlist.m3u8")
        
        self.assertIn("HLS manifest parsing failed", str(context.exception))


class TestConvertTimestampsIfNeeded(TestCase):
    """Test batch timestamp conversion."""

    @patch('app.utils.live_stream_time_converter.convert_epoch_to_relative_seconds')
    def test_convert_both_epoch_timestamps(self, mock_convert):
        """Test conversion when both timestamps are epoch."""
        mock_convert.side_effect = [300, 600]  # 5 and 10 minutes
        
        start_time, end_time = convert_timestamps_if_needed(
            1672574700, 1672575000, "http://example.com/playlist.m3u8"
        )
        
        self.assertEqual(start_time, 300)
        self.assertEqual(end_time, 600)
        self.assertEqual(mock_convert.call_count, 2)

    @patch('app.utils.live_stream_time_converter.convert_epoch_to_relative_seconds')
    def test_convert_only_start_epoch(self, mock_convert):
        """Test conversion when only start_time is epoch (end_time is None)."""
        mock_convert.return_value = 300

        start_time, end_time = convert_timestamps_if_needed(
            1672574700, None, "http://example.com/playlist.m3u8"
        )

        self.assertEqual(start_time, 300)
        self.assertIsNone(end_time)
        mock_convert.assert_called_once_with(1672574700, "http://example.com/playlist.m3u8")

    def test_no_conversion_needed(self):
        """Test when no conversion is needed (both are relative seconds)."""
        start_time, end_time = convert_timestamps_if_needed(
            300, 600, "http://example.com/playlist.m3u8"
        )
        
        self.assertEqual(start_time, 300)
        self.assertEqual(end_time, 600)

    def test_none_values(self):
        """Test handling of None values."""
        start_time, end_time = convert_timestamps_if_needed(
            None, None, "http://example.com/playlist.m3u8"
        )
        
        self.assertIsNone(start_time)
        self.assertIsNone(end_time)


class TestDetectTimestampTypes(TestCase):
    """Test timestamp type detection."""

    def test_both_epoch(self):
        """Test detection when both are epoch timestamps."""
        start_is_epoch, end_is_epoch = detect_timestamp_types(1672574700, 1672575000)
        self.assertTrue(start_is_epoch)
        self.assertTrue(end_is_epoch)

    def test_both_relative(self):
        """Test detection when both are relative seconds."""
        start_is_epoch, end_is_epoch = detect_timestamp_types(300, 600)
        self.assertFalse(start_is_epoch)
        self.assertFalse(end_is_epoch)

    def test_mixed_types(self):
        """Test detection with mixed timestamp types (for validation purposes)."""
        start_is_epoch, end_is_epoch = detect_timestamp_types(1672574700, 600)
        self.assertTrue(start_is_epoch)
        self.assertFalse(end_is_epoch)

    def test_none_values(self):
        """Test detection with None values."""
        start_is_epoch, end_is_epoch = detect_timestamp_types(None, None)
        self.assertFalse(start_is_epoch)
        self.assertFalse(end_is_epoch)

        start_is_epoch, end_is_epoch = detect_timestamp_types(1672574700, None)
        self.assertTrue(start_is_epoch)
        self.assertFalse(end_is_epoch)
