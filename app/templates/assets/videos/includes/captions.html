<div class="max-w-3xl" x-data="{
  showConfirmModal: false,
  isGenerating: {% if asset.video.generate_subtitle and not has_auto_generated_subtitle %}true{% else %}false{% endif %},
  showAlert: false,
  alertType: 'info',
  alertMessage: '',
  showAlertMessage(type, message) {
    this.alertType = type;
    this.alertMessage = message;
    this.showAlert = true;
  }
}">
  {% if active_captions %}
    <div class="relative mt-2">
      <dl class="grid grid-cols-1 mt-6 xl:grid-cols-1 gap-y-4 w-full" x-show="isGenerating">
        {% include 'assets/videos/includes/generating_subtitle_alert.html' %}
      </dl>

      <div class="flex justify-between items-center mt-4">
        <h3 class="text-sm font-semibold leading-7 text-gray-900">Captions</h3>
        <div class="flex items-center gap-4">
          {% if not has_auto_generated_subtitle %}
            <div x-show="!isGenerating" class="inline-flex">
              <button
                type="button"
                @click="showConfirmModal = true"
                class="text-sm font-semibold leading-6 text-blue-600 hover:text-blue-500 inline-flex items-center"
              >
                <svg class="mr-1.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.8" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                </svg>
                Generate Auto Subtitle
              </button>
            </div>
          {% endif %}
          <button type="button" @click="showCreateCaptionModel = !showCreateCaptionModel"
            class="ml-4 text-sm font-semibold leading-6 text-blue-600 hover:text-blue-500"><span aria-hidden="true">&nbsp;+</span>
            Upload Caption</button>
        </div>
      </div>
      <div class="mt-2 text-sm text-gray-900 sm:col-span-2">
        <ul role="list" class="divide-y divide-gray-100 rounded-md border border-gray-200">
          {% for caption in active_captions %}
            {% include "./captions_list.html" %}
          {% endfor %}
        </ul>
      </div>
    </div>
  {% endif %}
  {% if disabled_captions %}
    <div class="relative" class="mt-2">
      <h2 class="mt-4 text-sm font-semibold leading-6 text-gray-900">Disabled</h3>
        <div class="mt-2 text-sm text-gray-900 sm:col-span-2">
          <ul role="list" class="divide-y divide-gray-100 rounded-md border border-gray-200">
            {% for caption in disabled_captions %}
              {% include "./captions_list.html" %}
            {% endfor %}
          </ul>
        </div>
    </div>
  {% endif %}
  {% include 'assets/videos/includes/generate_subtitle_modal.html' %}
</div>
