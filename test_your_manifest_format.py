#!/usr/bin/env python
"""
Test script to verify your specific HLS manifest format works correctly.
"""
import os
import sys
from unittest.mock import MagicMock, patch

# Add the project root to the Python path
sys.path.insert(0, '/Users/<USER>/workspace/tpstreams/streams')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.local')
import django
django.setup()

from datetime import datetime
import pytz
from app.utils.live_stream_time_converter import convert_timestamps_if_needed
from app.api.v1.serializers.live_stream import LiveStreamTrimRequestSerializer


def test_your_manifest_format():
    """Test with your actual manifest format."""
    print("=== Testing Your HLS Manifest Format ===")
    
    # Your manifest's first program date time
    program_date_time = "2025-07-11T12:06:36.519+0530"
    print(f"Program date time from manifest: {program_date_time}")
    
    # Parse it to get the stream start time in UTC
    # +0530 means UTC+5:30 (India Standard Time)
    # So 12:06:36.519+0530 = 06:36:36.519 UTC
    expected_utc = datetime(2025, 7, 11, 6, 36, 36, 519000, tzinfo=pytz.UTC)
    print(f"Expected UTC time: {expected_utc}")
    print(f"Expected UTC timestamp: {expected_utc.timestamp()}")
    
    # Mock the HLS parsing to return this time
    with patch('app.utils.live_stream_time_converter.get_hls_stream_start_time') as mock_get_start_time:
        mock_get_start_time.return_value = expected_utc
        
        # Test epoch conversion
        print("\n--- Testing Epoch Conversion ---")
        
        # Test with epoch timestamp 5 minutes after stream start
        epoch_5min_later = int(expected_utc.timestamp()) + 300
        print(f"Epoch timestamp (5 min later): {epoch_5min_later}")
        
        start_time, end_time = convert_timestamps_if_needed(
            epoch_5min_later, None, "http://example.com/playlist.m3u8"
        )
        
        print(f"Converted start_time: {start_time} seconds")
        print(f"Expected: ~300 seconds (5 minutes)")
        
        if abs(start_time - 300) <= 1:  # Allow 1 second tolerance for fractional seconds
            print("✓ Conversion successful!")
        else:
            print(f"❌ Conversion failed: expected ~300, got {start_time}")
    
    # Test with serializer
    print("\n--- Testing Serializer Integration ---")
    
    mock_live_stream = MagicMock()
    mock_live_stream.get_hls_url = "https://cdn.example.com/live/org/asset/video.m3u8"
    duration = 3600  # 1 hour
    
    with patch('app.utils.live_stream_time_converter.get_hls_stream_start_time') as mock_get_start_time:
        mock_get_start_time.return_value = expected_utc
        
        # Test with epoch timestamps
        data = {
            "start_time": epoch_5min_later,  # 5 minutes after start
            "end_time": int(expected_utc.timestamp()) + 600  # 10 minutes after start
        }
        
        print(f"Input data: {data}")
        
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=duration, live_stream=mock_live_stream
        )
        
        if serializer.is_valid():
            validated = serializer.validated_data
            print(f"✓ Serializer validation successful!")
            print(f"  Converted start_time: {validated['start_time']} seconds")
            print(f"  Converted end_time: {validated['end_time']} seconds")
            print(f"  Using epoch: {validated['_using_epoch']}")
        else:
            print(f"❌ Serializer validation failed: {serializer.errors}")


def test_api_usage_example():
    """Show how the API would be used with your timestamps."""
    print("\n=== API Usage Example ===")
    
    # Your stream starts at 2025-07-11T12:06:36.519+0530
    # Which is 2025-07-11T06:36:36.519Z in UTC
    # UTC timestamp: 1752215796.519
    
    stream_start_utc_timestamp = 1752215796
    
    print("Example API requests:")
    print()
    
    print("1. Trim from 2 minutes to 5 minutes after stream start:")
    start_epoch = stream_start_utc_timestamp + 120  # 2 minutes
    end_epoch = stream_start_utc_timestamp + 300    # 5 minutes
    print(f"   POST /api/v1/organizations/{{org_id}}/assets/{{asset_id}}/stop-live-stream/")
    print(f"   {{")
    print(f'     "start_time": {start_epoch},')
    print(f'     "end_time": {end_epoch}')
    print(f"   }}")
    print()
    
    print("2. Trim from stream start to 3 minutes:")
    end_epoch = stream_start_utc_timestamp + 180    # 3 minutes
    print(f"   {{")
    print(f'     "end_time": {end_epoch}')
    print(f"   }}")
    print()
    
    print("3. Trim from 1 minute to end of stream:")
    start_epoch = stream_start_utc_timestamp + 60   # 1 minute
    print(f"   {{")
    print(f'     "start_time": {start_epoch}')
    print(f"   }}")


if __name__ == "__main__":
    print("Testing Your HLS Manifest Format")
    print("=" * 40)
    
    try:
        test_your_manifest_format()
        test_api_usage_example()
        
        print("\n" + "=" * 40)
        print("🎉 ALL TESTS PASSED!")
        print("\nYour manifest format is now supported!")
        print("✓ Timezone offset parsing (+0530)")
        print("✓ Epoch timestamp conversion")
        print("✓ Serializer validation")
        print("✓ API integration ready")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
