from django.db import models
from django.db.models import Case, IntegerField, Value, When
from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from django_multitenant.fields import TenantForeign<PERSON>ey
from mptt.querysets import TreeQuerySet
from safedelete.queryset import SafeDeleteQueryset
from taggit.managers import TaggableManager

from app.domain.storages import get_total_size
from app.domain.video import get_source_video_download_url
from app.tasks import rebuild_asset_tree, send_data_to_webhook_task
from app.utils.uuid import populate_uuid

from .base import OrganizationModel, OrganizationTreeManager, OrganizationTreeModel
from .fields import CreatedByField, TenantTreeForeignKey
from .tag import TaggedAsset


class AssetQuerySet(SafeDeleteQueryset, TreeQuerySet):
    def sort_folders_first(self):
        return self.annotate(
            is_folder=Case(
                When(type=Asset.Type.FOLDER, then=Value(True)),
                default=Value(False),
                output_field=IntegerField(),
            )
        ).order_by("-is_folder", *self.query.order_by)


class Asset(OrganizationTreeModel):
    class Type(models.IntegerChoices):
        VIDEO = 0, _("video")
        IMAGE = 1, _("image")
        LIVESTREAM = 2, _("livestream")
        AUDIO = 3, _("audio")
        FOLDER = 4, _("folder")

    uuid = models.CharField(max_length=36, editable=False, null=False)  # type: ignore
    title = models.CharField(max_length=1024, null=True, blank=True, db_index=True)
    type = models.PositiveSmallIntegerField(
        choices=Type.choices, default=Type.VIDEO, db_index=True
    )
    created_by = CreatedByField()
    bytes = models.PositiveBigIntegerField(null=True, blank=True)
    tags = TaggableManager(blank=True, through=TaggedAsset)
    parent = TenantTreeForeignKey(
        "self", null=True, blank=True, on_delete=models.CASCADE, related_name="children"
    )
    children_count = models.PositiveBigIntegerField(default=0)
    last_uploaded = models.DateTimeField(null=True, blank=True)
    views_count = models.PositiveBigIntegerField(default=0, null=True, db_index=True)
    average_watched_time = models.PositiveBigIntegerField(
        default=0,
        null=True,
        help_text="The average watched time in seconds.",
    )
    total_watch_time = models.PositiveBigIntegerField(
        default=0,
        null=True,
        help_text="The total watched time in seconds.",
    )
    unique_viewers_count = models.PositiveBigIntegerField(
        default=0,
        null=True,
        db_index=True,
        help_text="The number of users who have watched the video for the first time.",
    )

    unique_completed_views_count = models.PositiveBigIntegerField(
        default=0,
        null=True,
        help_text="Number of unique visitors who watched video till the end.",
    )

    top_drop_off_point = models.PositiveBigIntegerField(
        default=0,
        null=True,
        help_text="Most common time point (in seconds) where viewers stop watching.",
    )

    disable_domain_restriction = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        help_text="If true, this asset will bypass domain restrictions.",
    )

    objects = OrganizationTreeManager.from_queryset(AssetQuerySet)()

    class MPTTMeta:
        order_insertion_by = ["title"]

    def __str__(self):
        return self.title or ""

    def notify_webhook(self):
        from app.models import Webhook

        if not Webhook.objects.exists():
            return

        for webhook in Webhook.objects.filter(organization=self.organization):
            send_data_to_webhook_task.delay(
                webhook.url,
                self.organization.uuid,
                asset_uuid=self.uuid,
                token=webhook.secret_token,
            )

    def update_children_count(self):
        self.children_count = self.children.count()
        self.save(update_fields=["children_count"])

    def move(self, destination):
        if destination is None or destination.type == Asset.Type.FOLDER:
            previous_parent = self.parent
            self.parent = destination
            self.save()
            self.update_assets_children_count([previous_parent, destination])
            if destination:
                rebuild_asset_tree.delay(destination.uuid)
            if previous_parent:
                rebuild_asset_tree.delay(previous_parent.uuid)

    def update_assets_children_count(self, assets):
        for asset in assets:
            if asset:
                asset.update_children_count()

    def update_size(self):
        self.bytes = get_total_size(self)
        self.save(update_fields=["bytes"])

    def get_status(self):
        if hasattr(self, "video"):
            return self.video.get_status_display()
        elif hasattr(self, "live_stream"):
            return self.live_stream.get_status_display()

    def get_download_url(self):
        if hasattr(self, "video"):
            video_input = self.video.inputs.first()
            return get_source_video_download_url(video_input)

    @property
    def has_drm_encrypted_content(self):
        if hasattr(self, "video"):
            return self.video.is_drm_encrypted
        elif hasattr(self, "live_stream"):
            return self.live_stream.enable_drm
        return False

    @property
    def viewers_engagement_score(self):
        if self.unique_viewers_count > 0 and self.unique_completed_views_count > 0:
            return round(
                ((self.unique_completed_views_count / self.unique_viewers_count) * 100),
                1,
            )
        return 0


@receiver(pre_save, sender=Asset)
def populate_asset_uuid(sender, instance, **kwargs):
    populate_uuid(
        instance,
        length=11,
        alphabet="ABCDEFGHJKMNPQRSTUXYZabcdefghjkmnpqrstuxyz23456789",
    )


class AssetViewerLog(OrganizationModel):
    class DEVICE(models.IntegerChoices):
        MOBILE = 0
        SYSTEM = 1
        TABLET = 2
        OTHER = 3

    class PLATFORM(models.IntegerChoices):
        IOS = 0
        ANDROID = 1
        WEB = 2
        OTHER = 3

    class CLIENT(models.IntegerChoices):
        IOS = 0
        ANDROID = 1
        FLUTTER = 2
        BROWSER = 3
        OTHER = 4

    asset = TenantForeignKey(
        "app.Asset", on_delete=models.CASCADE, related_name="views"
    )
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    visitor_id = models.CharField(max_length=32, db_index=True)
    session_id = models.CharField(max_length=40)
    duration = models.PositiveBigIntegerField(
        default=0, help_text="Duration taken to view the video in seconds."
    )
    location = models.CharField(max_length=255, blank=True, null=True)
    device = models.PositiveSmallIntegerField(choices=DEVICE.choices, null=True)
    platform = models.PositiveSmallIntegerField(choices=PLATFORM.choices, null=True)
    client = models.PositiveSmallIntegerField(choices=CLIENT.choices, null=True)
    user_agent = models.CharField(max_length=1024, blank=True, null=True)
