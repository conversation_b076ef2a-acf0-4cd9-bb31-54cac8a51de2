"""
Tests for LiveStreamTrimRequestSerializer with epoch timestamp support.
"""
from datetime import datetime
from unittest.mock import patch, MagicMock

import pytz
from django.test import TestCase
from rest_framework.exceptions import ValidationError

from app.api.v1.serializers.live_stream import LiveStreamTrimRequestSerializer
from app.utils.live_stream_time_converter import LiveStreamTimeConversionError


class TestLiveStreamTrimRequestSerializer(TestCase):
    """Test the LiveStreamTrimRequestSerializer with epoch support."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_live_stream = MagicMock()
        self.mock_live_stream.get_hls_url = "http://example.com/live/org/asset/video.m3u8"
        self.duration = 3600  # 1 hour

    def test_relative_seconds_validation(self):
        """Test validation with relative seconds (existing behavior)."""
        data = {"start_time": 300, "end_time": 600}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data
        self.assertEqual(validated_data["start_time"], 300)
        self.assertEqual(validated_data["end_time"], 600)
        self.assertFalse(validated_data["_start_was_epoch"])
        self.assertFalse(validated_data["_end_was_epoch"])

    def test_relative_seconds_with_defaults(self):
        """Test validation with relative seconds using defaults."""
        data = {}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data
        self.assertEqual(validated_data["start_time"], 0)
        self.assertEqual(validated_data["end_time"], self.duration)

    @patch('app.api.v1.serializers.live_stream.convert_timestamps_if_needed')
    def test_epoch_timestamps_validation(self, mock_convert):
        """Test validation with epoch timestamps."""
        mock_convert.return_value = (300, 600)  # Converted to relative seconds
        
        # Epoch timestamps: 2023-01-01 10:05:00 and 10:10:00 UTC
        data = {"start_time": 1672574700, "end_time": 1672575000}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data
        self.assertEqual(validated_data["start_time"], 300)
        self.assertEqual(validated_data["end_time"], 600)
        self.assertTrue(validated_data["_start_was_epoch"])
        self.assertTrue(validated_data["_end_was_epoch"])
        
        mock_convert.assert_called_once_with(
            1672574700, 1672575000, self.mock_live_stream.get_hls_url
        )

    @patch('app.api.v1.serializers.live_stream.convert_timestamps_if_needed')
    def test_mixed_timestamp_types(self, mock_convert):
        """Test validation with mixed timestamp types."""
        mock_convert.return_value = (300, 600)  # Only start_time converted
        
        data = {"start_time": 1672574700, "end_time": 600}  # Epoch + relative
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data
        self.assertEqual(validated_data["start_time"], 300)
        self.assertEqual(validated_data["end_time"], 600)
        self.assertTrue(validated_data["_start_was_epoch"])
        self.assertFalse(validated_data["_end_was_epoch"])

    def test_epoch_without_live_stream_fails(self):
        """Test that epoch timestamps fail without live_stream instance."""
        data = {"start_time": 1672574700, "end_time": 600}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration  # No live_stream provided
        )
        
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        
        self.assertIn("Live stream instance is required", str(context.exception))

    @patch('app.api.v1.serializers.live_stream.convert_timestamps_if_needed')
    def test_conversion_error_handling(self, mock_convert):
        """Test handling of conversion errors."""
        mock_convert.side_effect = LiveStreamTimeConversionError("Manifest parsing failed")
        
        data = {"start_time": 1672574700, "end_time": 1672575000}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        
        self.assertIn("Time conversion failed", str(context.exception))

    @patch('app.api.v1.serializers.live_stream.convert_timestamps_if_needed')
    def test_converted_time_out_of_range(self, mock_convert):
        """Test validation when converted time is out of range."""
        mock_convert.return_value = (4000, 600)  # start_time > duration
        
        data = {"start_time": 1672574700, "end_time": 600}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        
        error_message = str(context.exception)
        self.assertIn("Converted start_time", error_message)
        self.assertIn("out of valid range", error_message)
        self.assertIn("Original epoch timestamp", error_message)

    def test_relative_seconds_out_of_range(self):
        """Test validation with relative seconds out of range."""
        data = {"start_time": -100, "end_time": 600}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        
        self.assertIn("start_time is out of valid range", str(context.exception))

    def test_start_time_greater_than_end_time(self):
        """Test validation when start_time >= end_time."""
        data = {"start_time": 600, "end_time": 300}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        
        self.assertIn("start_time must be less than end_time", str(context.exception))

    def test_no_duration_fails(self):
        """Test that validation fails without duration."""
        data = {"start_time": 300, "end_time": 600}
        serializer = LiveStreamTrimRequestSerializer(data=data)
        
        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)
        
        self.assertIn("Duration is required for validation", str(context.exception))

    def test_only_start_time_provided(self):
        """Test validation with only start_time provided."""
        data = {"start_time": 300}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data
        self.assertEqual(validated_data["start_time"], 300)
        self.assertEqual(validated_data["end_time"], self.duration)

    def test_only_end_time_provided(self):
        """Test validation with only end_time provided."""
        data = {"end_time": 600}
        serializer = LiveStreamTrimRequestSerializer(
            data=data, duration=self.duration, live_stream=self.mock_live_stream
        )
        
        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data
        self.assertEqual(validated_data["start_time"], 0)
        self.assertEqual(validated_data["end_time"], 600)
