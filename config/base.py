import os
from pathlib import Path

import environ

from .custom_template_tag_loader import get_custom_template_tags

env = environ.Env(
    # set casting, default value
    DEBUG=(bool, False)
)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Take environment variables from .env file
environ.Env.read_env(os.path.join(BASE_DIR, ".env"), overwrite=True)

# False if not in os.environ because of casting above
DEBUG = env("DEBUG")

# Raises Django's ImproperlyConfigured
# exception if SECRET_KEY not in os.environ
SECRET_KEY = env("SECRET_KEY")

# Application definition

LOCAL_APPS = [
    "app.apps.StreamConfig",
]

DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.sites",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    "django.forms",
]

THIRD_PARTY_APPS = [
    "safedelete",
    "django_extensions",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "django_browser_reload",
    "tailwind",
    "widget_tweaks",
    "mptt",
    "django_multitenant",
    "rest_framework",
    "knox",
    "import_export",
    "corsheaders",
    "phonenumber_field",
    "utm_tracker",
    "django_filters",
]

INSTALLED_APPS = LOCAL_APPS + DJANGO_APPS + THIRD_PARTY_APPS

MIGRATION_MODULES = {"app": "app.db.migrations"}

# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "app.middlewares.MultitenantMiddleware",
    "app.middlewares.TimezoneMiddleware",
    "utm_tracker.middleware.UtmSessionMiddleware",
    "utm_tracker.middleware.LeadSourceMiddleware",
    "app.middlewares.CheckOrganizationStatusMiddleware",
]

ROOT_URLCONF = "config.urls"

CUSTOM_TEMPLATE_TAG_FOLDERS = ["app.helpers"]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
            ],
            "builtins": [
                "app.helpers.builtins",
                "app.helpers.bytes",
                "app.helpers.time",
                "app.helpers.math",
            ],
            "libraries": get_custom_template_tags(CUSTOM_TEMPLATE_TAG_FOLDERS),
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Kolkata"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = "static/"

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {"default": env.db()}
DATABASES["default"]["ENGINE"] = "django_multitenant.backends.postgresql"
DATABASES["default"]["CONN_MAX_AGE"] = env.int("CONN_MAX_AGE", default=60)  # noqa F405

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = True

ACCOUNT_USER_MODEL_USERNAME_FIELD = None
AUTH_USER_MODEL = "app.User"
ACCOUNT_AUTHENTICATION_METHOD = "email"
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_LOGOUT_ON_GET = True
ACCOUNT_MAX_EMAIL_ADDRESSES = 1
ACCOUNT_CONFIRM_EMAIL_ON_GET = True
ACCOUNT_LOGIN_ON_PASSWORD_RESET = True
ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE = False
ACCOUNT_FORMS = {"signup": "app.forms.SignupForm"}
ACCOUNT_EMAIL_VERIFICATION = "none"

TAILWIND_APP_NAME = "app"

LOGIN_REDIRECT_URL = "/"

REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 50,
    "DEFAULT_THROTTLE_RATES": {
        "track_analytics_throttle": "3/minute",
    },
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
}

REST_KNOX = {
    "TOKEN_TTL": None,
}

TRANSCODER_URL = env.str("LUMBERJACK_URL", default="")
LUMBERJACK_AUTH_TOKEN = env.str("LUMBERJACK_AUTH_TOKEN", default="")
WASABI_S3_ENDPOINT_URL = env.str("WASABI_S3_ENDPOINT_URL", default="")
WASABI_ACCESS_KEY_ID = env.str("WASABI_ACCESS_KEY_ID", default="")
WASABI_SECRET_ACCESS_KEY = env.str("WASABI_SECRET_ACCESS_KEY", default="")
WASABI_BUCKET_REGION = env.str("WASABI_BUCKET_REGION", default="")
DRM_SIGNER = env("DRM_SIGNER")
AES_SIGNING_IV = env("AES_SIGNING_IV")
AES_SIGNING_KEY = env("AES_SIGNING_KEY")
CONTENT_KEY_URL = env("CONTENT_KEY_URL")
VDO_CIPHER_API_KEY = env("VDO_CIPHER_API_KEY")
FAIRPLAY_CERTIFICATE_URL = env("FAIRPLAY_CERTIFICATE_URL")
ANALYTICS_LOG_BUCKET = env("ANALYTICS_LOG_BUCKET")

DRM_LICENSE_EXPIRY = 60 * 60 * 10  # 1 Hour
DRM_LICENSE_OFFLINE_DOWNLOAD_EXPIRY = 15 * 24 * 3600  # 15 days

CELERY_BROKER_URL = env.str("CELERY_BROKER_URL", default="")
CELERY_RESULT_BACKEND = env.str("CELERY_RESULT_BACKEND", default="")

KNOX_TOKEN_MODEL = "app.AuthToken"
CLOUDFRONT_PUBLIC_KEY = env.str("CLOUDFRONT_PUBLIC_KEY", default="")
CLOUDFRONT_DEFAULT_CACHE_POLICY = env.str("CLOUDFRONT_DEFAULT_CACHE_POLICY", default="")
AWS_ACCESS_KEY_ID = env.str("AWS_ACCESS_KEY_ID", default="")
AWS_SECRET_ACCESS_KEY = env.str("AWS_SECRET_ACCESS_KEY", default="")
TPSTREAMS_AWS_ACCESS_KEY_ID = env.str("TPSTREAMS_AWS_ACCESS_KEY_ID", default="")
TPSTREAMS_AWS_SECRET_ACCESS_KEY = env.str("TPSTREAMS_AWS_SECRET_ACCESS_KEY", default="")
DEFAULT_FROM_EMAIL = "Testpress <<EMAIL>>"
SUPPORT_EMAILS = [
    "<EMAIL>",
]
DEVELOPER_EMAILS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]
SERVER_EMAIL = env("DJANGO_SERVER_EMAIL", default=DEFAULT_FROM_EMAIL)
EMAIL_HOST = "email-smtp.ap-south-1.amazonaws.com"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = env("SMTP_USERNAME", default="")
EMAIL_HOST_PASSWORD = env("SMTP_PASSWORD", default="")

LIVE_STREAMING_SERVER_IMAGE_ID = env.str("LIVE_STREAMING_SERVER_IMAGE_ID", default="")
HETZNER_API_TOKEN = env.str("HETZNER_API_TOKEN", default="")

LIVE_STREAM_IP = env.str("LIVE_STREAM_IP", default="")

WIDEVINE_AES_KEY = env.str("WIDEVINE_AES_KEY")
WIDEVINE_IV = env.str("WIDEVINE_IV")
WIDEVINE_CONTENT_KEY_URL = "https://license.widevine.com/cenc/getcontentkey/testpress"
WIDEVINE_LICENSE_KEY_URL = "https://license.widevine.com/cenc/getlicense/testpress"
FAIRPLAY_PRIVATE_KEY = env.str("FAIRPLAY_PRIVATE_KEY", multiline=True).encode()
FAIRPLAY_PASSWORD = env.str("FAIRPLAY_PASSWORD", default="").encode()
FAIRPLAY_ASK = env.str("FAIRPLAY_ASK", default="")

SITE_URL = "https://app.tpstreams.com"
LICENSE_SERVER_URL = "https://license.tpstreams.com"

SHELL_PLUS_IMPORTS = [
    "from django_multitenant.utils import set_current_tenant, unset_current_tenant, get_current_tenant",
]
CORS_ALLOW_ALL_ORIGINS = True
CORS_URLS_REGEX = r"^/api/(v1|uploader)/.*$"

DIGITAL_OCEAN_API_KEY = env.str("DIGITAL_OCEAN_API_KEY", default="")
LIVE_STREAM_DROPLET_SNAPSHOT = "186563704"
LIVE_STREAM_SERVER_PROVIDER = "aws"
LIVE_STREAM_AWS_SNAPSHOT = "ami-075bbf47edcd5a376"
SUBTITLE_SERVER_KEY_PAIR_NAME = env.str("SUBTITLE_SERVER_KEY_PAIR_NAME", default="")
PROXY_API_KEY = env.str("PROXY_API_KEY")
PROXY_URL = "http://live.tpstreams.com:5555"
CHAT_URL = env("CHAT_URL", default="")
CHAT_ANONYMOUS_USER_KEY = env("CHAT_ANONYMOUS_USER_KEY", default="")
CHAT_ADMIN_KEY = env("CHAT_ADMIN_KEY", default="")

MASTER_PASSWORD = env("MASTER_PASSWORD", default="")

UTM_TRACKER_CUSTOM_TAGS = ["keyword", "ad_group", "referrer"]

DEFAULT_CLOUDFRONT_PRIVATE_KEY = env.str(
    "DEFAULT_CLOUDFRONT_PRIVATE_KEY", default="", multiline=True
)
NPO_CLOUDFRONT_PRIVATE_KEY = env.str(
    "NPO_CLOUDFRONT_PRIVATE_KEY", default="", multiline=True
)


EC2_LIVESTREAM_VOLUME_ID = "snap-0fb66c6b9826b4e2a"
LIVE_STREAM_S3_BUCKET = "livestreaming-tpstreams"

CDN_ONE_YEAR_CACHE_POLICY_ID = "d8d9fe18-407a-4999-ad00-29025fc0c497"
CDN_EXPIRE_IN_3_SECONDS_CACHE_POLICY_ID = "18b0d37a-529c-4f4a-b38c-6816ad22433c"

MAXIMUM_THUMBNAIL_SIZE_BYTES = 2 * 1024 * 1024
MAXIMUM_THUMBNAIL_COUNT = 7


USE_ACCESS_TOKEN_FROM_CACHE = [
    "4tn35u",
    "gcma48",
    "dcek2m",
    "edee9b",
    "96sbdx",
    "9mpasc",
    "n2nbhp",
    "69uh4g",
    "4u66uy",
    "4khc8z",
    "hzm5aq",
    "da3eee",
    "b5crb3",
    "48bqht",
    "8kjfmh",
    "k23jbj",
    "7s9s66",
    "87r52e",
    "ksh9he",
    "agg625",
    "6ggjua",
    "352dct",
    "6eafqn",
    "kftsfr",
    "7a9xf3",
    "9q94nm",
    "a83mdr",
    "khcuyh",
]

CASSANDRA_SERVER_URL = env.str("CASSANDRA_SERVER_URL", default="")
PLAYER_SENTRY_URL = "https://<EMAIL>/12"
TESTPRESS_AUTH_TOKEN = "nXh2*Z>80141"
TPSTREAMS_AWS_DOCKER_INSTALLED_AMI = env.str(
    "TPSTREAMS_AWS_DOCKER_INSTALLED_AMI", default=""
)
TPSTREAMS_AWS_PROXY_SECURITY_GROUP_ID = env.str(
    "TPSTREAMS_AWS_PROXY_SECURITY_GROUP_ID", default=""
)
TPSTREAMS_AWS_PROXY_SERVER_KEY_PAIR_NAME = env.str(
    "TPSTREAMS_AWS_PROXY_SERVER_KEY_PAIR_NAME", default=""
)
TPSTREAMS_AWS_PROXY_SUBNET_ID = env.str("TPSTREAMS_AWS_PROXY_SUBNET_ID", default="")
TPSTREAMS_AWS_OPENRESTY_PROXY_API_KEY = env.str(
    "TPSTREAMS_AWS_OPENRESTY_PROXY_API_KEY", default=""
)
TPSTREAMS_AWS_OPENRESTY_PROXY_URL = "http://live2.tpstreams.com:8080"
TPSTREAMS_LINODE_OPENRESTY_PROXY_URL = "http://live-linode.tpstreams.com:8080"

DURATION_TO_AUTO_TERMINATE = 7200


TPSTREAMS_LINODE_API_TOKEN = (
    "92b3e193bbd5153821850b3dcf1060000c6eef12821d01a55f94e08ef0442c99"
)
TPSTREAMS_LINODE_REGION = "in-bom-2"
TPSTREAMS_LINODE_ROOT_PASSWORD = "3EcT1IY2EFR1"

TPSTREAMS_DIGITALOCEAN_OPENRESTY_PROXY_URL = (
    "http://live-digitalocean.tpstreams.com:8080"
)

USE_DIGITALOCEAN_FOR_LIVE_STREAMING = ["9q94nm", "ehqz93", "96dd72", "enqc36"]

ZOOM_CLIENT_ID = "dnioVx5JTomRkHXhtIvbfA"
ZOOM_CLIENT_SECRET = "9nX3TEo2KVBEuVC9J5dPYMpAWy8gqi5v"
ZOOM_WEBHOOK_SECRET = "TZQt_S9CQfie4qfbH4ZEIw"
